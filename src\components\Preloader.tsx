import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import logo from '../images/logo.webp'; // Assuming you have a logo image

interface PreloaderProps {
  onComplete: () => void;
}

const Preloader: React.FC<PreloaderProps> = ({ onComplete }) => {
  const [isAnimating, setIsAnimating] = useState(true);
  
  useEffect(() => {
    console.log('🎬 Preloader started');
    const timer = setTimeout(() => {
      console.log('⏰ Preloader animation ending');
      setIsAnimating(false);
      setTimeout(() => {
        console.log('✅ Preloader calling onComplete');
        onComplete();
      }, 1000); // Delay for fade out animation
    }, 4000); // Increased time to allow for logo animation
    
    return () => {
      console.log('🧹 Preloader cleanup');
      clearTimeout(timer);
    };
  }, [onComplete]);
  
  // Container variants for the overall animation
  const containerVariants = {
    initial: { 
      opacity: 1,
      scale: 0.8,
      filter: "drop-shadow(0px 0px 0px rgba(212, 175, 55, 0))" 
    },
    animate: { 
      opacity: 1,
      scale: 1,
      filter: "drop-shadow(0px 0px 20px rgba(212, 175, 55, 0.8))",
      transition: {
        duration: 2,
        ease: "easeOut"
      }
    },
    glow: {
      filter: [
        "drop-shadow(0px 0px 20px rgba(212, 175, 55, 0.8))",
        "drop-shadow(0px 0px 30px rgba(212, 175, 55, 1))",
        "drop-shadow(0px 0px 20px rgba(212, 175, 55, 0.8))"
      ],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut",
        delay: 2.5
      }
    }
  };

  // Text variant for the brand name
  const textVariant = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 1,
        delay: 2.5,
        ease: "easeOut"
      }
    }
  };

  // Typewriter animation states
  const [titleText, setTitleText] = useState("");
  const [subtitleText, setSubtitleText] = useState("");
  const fullTitle = "BRILLAR";
  const fullSubtitle = "非日常へようこそ";

  // Typewriter effect
  useEffect(() => {
    let titleIndex = 0;
    let subtitleIndex = 0;
    
    // Start title animation after logo appears
    const titleTimer = setTimeout(() => {
      const titleInterval = setInterval(() => {
        if (titleIndex < fullTitle.length) {
          setTitleText(fullTitle.slice(0, titleIndex + 1));
          titleIndex++;
        } else {
          clearInterval(titleInterval);
          // Start subtitle animation after title completes
          const subtitleInterval = setInterval(() => {
            if (subtitleIndex < fullSubtitle.length) {
              setSubtitleText(fullSubtitle.slice(0, subtitleIndex + 1));
              subtitleIndex++;
            } else {
              clearInterval(subtitleInterval);
            }
          }, 80); // Speed for subtitle
        }
      }, 150); // Speed for title
    }, 2500); // Delay before starting typewriter

    return () => clearTimeout(titleTimer);
  }, []);
  
  return (
    <motion.div 
      className="fixed inset-0 flex items-center justify-center bg-[#1a1a1a] z-[9999]"
      initial={{ opacity: 1 }}
      animate={{ opacity: isAnimating ? 1 : 0 }}
      transition={{ duration: 1 }}
    >
      <div className="flex flex-col items-center">
        {/* Logo Container */}
        <motion.div
          className="w-48 h-48 md:w-64 md:h-64 flex items-center justify-center mb-8"
          variants={containerVariants}
          initial="initial"
          animate={["animate", "glow"]}
        >
          {/* Your uploaded logo */}
          <motion.img
            src={logo}
            className="w-full h-full object-contain"
            initial={{ opacity: 0, scale: 0.5, rotate: -10 }}
            animate={{ 
              opacity: 1, 
              scale: 1, 
              rotate: 0,
              transition: {
                duration: 1.5,
                ease: "easeOut",
                delay: 0.5
              }
            }}
          />
        </motion.div>

        {/* Brand Name */}
        <motion.div
          className="text-center"
          variants={textVariant}
          initial="hidden"
          animate="visible"
        >
          <h1 className="font-serif text-3xl md:text-4xl text-gold tracking-[0.2em] mb-2">
            BRILLAR
          </h1>
          <p className="font-serif-jp text-lg md:text-xl text-gold-light opacity-80">
            非日常へようこそ
          </p>
        </motion.div>

        {/* Loading indicator */}
        <motion.div
          className="mt-8 flex space-x-1"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 3 }}
        >
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              className="w-2 h-2 bg-gold rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                delay: index * 0.2
              }}
            />
          ))}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Preloader;
