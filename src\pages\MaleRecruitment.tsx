import React, { useEffect } from "react";
import { motion } from "framer-motion";
import { useLanguage } from "@/hooks/use-language";
import { Card, CardContent } from "@/components/ui/card";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MapPin, Clock, Users, Star, Briefcase, GraduationCap, Phone, MessageCircle, FileText, Send } from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import heroImage from "../images/brillar-male-hero.webp";



const MaleRecruitment: React.FC = () => {
  const { t } = useLanguage();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Sample gallery images
  const galleryImages = [
    "https://images.unsplash.com/photo-1605810230434-7631ac76ec81?w=800&q=80",
    "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800&q=80",
    "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80",
    "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&q=80",
    "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=800&q=80",
    "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&q=80"
  ];

  const infoSections = [
    {
      title: "スタッフ求人PR",
      description: "現在、幹部ポストに空きがあり、幹部候補としてしっかり稼ぎたい方や、女性スタッフも大募集中！女性も活躍できる職場です☆\n私たちは山梨県・甲府市を拠点に活動しています。\n未経験者OK！経験者の方は優遇いたします。能力に見合った給与をお約束します。\nアルバイトの募集もございますので、学生さん、Wワーク希望の方、フリーターさんも大歓迎！体験給与は全額日払いなので、まずは体験入社だけでもお気軽にお越しください。\nご質問などがありましたら、どうぞお気軽にお問い合わせください。"
    },
    {
      title: "社員経歴紹介①",
      description: "当社では多様なバックグラウンドを持つスタッフが活躍しています。未経験からスタートして、今では店舗運営の中核を担うマネージャーまで成長したスタッフも多数在籍。あなたの経験やスキルを活かせる環境を整えています。"
    },
    {
      title: "社員経歴紹介②",
      description: "チームワークを重視し、お互いを支え合いながら成長できる職場環境です。先輩スタッフによる丁寧な指導と、充実した研修プログラムで、安心してキャリアをスタートできます。長期的なキャリア形成もサポートいたします。"
    },

  ];

  const overviewData = [
    { label: "職種", value: "男女ホールスタッフ" },
    { label: "勤務地", value: "〒400-0032\n山梨県甲府市中央1-12-5五光ビル 4F" },
    { label: "仕事内容", value: "店長・幹部候補 正社員\n- ホール業務、店舗の運営や企画の提案、スタッフの管理やマネージャー業務など「店舗責任者」としての仕事をお任せします。まずは店長を目指して、経験を積んでステップアップしてくださいね♪ 経験者の方は、今までの経験や前店給与考慮致します。\n\n接客/ホール アルバイト\n- お客様のご案内、ドリンクやフードの提供、簡単なウエイター業務など 未経験の方でも、頑張り次第では随時昇給昇格もあるので稼げるチャンス★\n\nエスコート アルバイト\n- ご来店されたお客様のご案内など、エントランスでの業務をお願いします。" },
    { label: "資格", value: "18歳以上(高校生不可)\n未経験・初心者OK、経験者・有資格者歓迎、学生歓迎、フリーター歓迎、主婦・主夫歓迎、ブランクOK、副業・WワークOK、無資格でもOK、学歴不問" },
    { label: "給与", value: "– 店長・店長候補(社員)\n月給 500,000円〜\n– ホール(社員)\n月給 300,000円〜\n– ホール(アルバイト)\n時間報酬 1,300円〜" },
    { label: "勤務時間", value: "社員 19:00～Last\nアルバイト 20:00～Last\n(シフト制、営業時間内で3時間～勤務可能)" },
    { label: "休日", value: "日・GW・夏休暇・年末年始休暇" },
    { label: "待遇", value: "年齢不問\n経験者優遇\n未経験者歓迎\n昇給昇格随時あり\n社員旅行あり\n独立支援制度あり\n研修制度あり\n中高年歓迎\n制服貸与\n体験入社OK\n正社員登用\n社保完備" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-black via-gray-900 to-black">
      <Navbar />

      {/* Hero Section */}
      <section className="relative h-screen w-full overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url(${heroImage})`,
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/90 via-black/60 to-black/90" />

        <div className="relative z-10 flex items-center justify-center h-full px-4">
          <motion.div
            className="text-center max-w-4xl"
            initial={{ opacity: 0, y: 60 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1.2 }}
          >
            <motion.h1
              className="text-6xl md:text-8xl font-bold mb-6"
              style={{
                background: 'linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #D4AF37 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 0 60px rgba(212, 175, 55, 0.8)',
              }}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.3 }}
            >
              男性スタッフ募集
            </motion.h1>

            <motion.p
              className="text-2xl md:text-3xl text-gold-light mb-8 font-serif"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              あなたの可能性を最大限に引き出す
            </motion.p>

            <motion.div
              className="flex flex-wrap justify-center gap-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.9 }}
            >
              <Badge className="bg-gold/20 text-gold border-gold/40 hover:bg-gold hover:text-black transition-all duration-300 px-4 py-2 text-lg">
                学歴・職歴・年齢不問
              </Badge>
              <Badge className="bg-gold/20 text-gold border-gold/40 hover:bg-gold hover:text-black transition-all duration-300 px-4 py-2 text-lg">
                未経験者歓迎
              </Badge>

            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Info Sections */}
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <motion.h2
            className="text-4xl md:text-6xl font-bold text-center mb-16 text-gold"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            働く環境について
          </motion.h2>

          <div className="space-y-16">
            {infoSections.map((section, index) => (
              <motion.div
                key={index}
                className="max-w-4xl mx-auto text-left"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <h3 className="text-3xl md:text-4xl font-bold text-gold mb-8">{section.title}</h3>
                <p className="text-gray-300 leading-relaxed text-lg md:text-xl whitespace-pre-line">{section.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Carousel */}

      {/* Overview Section */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <motion.h2
            className="text-4xl md:text-6xl font-bold text-center mb-16 text-gold"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            Overview／業務概要
          </motion.h2>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card className="bg-black/40 border-gold/30 backdrop-blur-sm">
              <CardContent className="p-0">
                <div className="overflow-hidden">
                  {overviewData.map((item, index) => (
                    <motion.div
                      key={index}
                      className="grid grid-cols-1 md:grid-cols-4 border-b border-gold/20 last:border-b-0"
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <div className="bg-gold/10 p-6 border-r border-gold/20 md:col-span-1">
                        <h3 className="font-bold text-gold text-lg">{item.label}</h3>
                      </div>
                      <div className="p-6 md:col-span-3">
                        <p className="text-gray-300 leading-relaxed whitespace-pre-line">{item.value}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Contact Call to Action */}
          <motion.div
            className="text-center mt-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl md:text-3xl font-bold text-gold mb-6">
              ご応募・お問い合わせはこちら
            </h3>
            <div className="flex flex-col md:flex-row justify-center items-center gap-8">
              <div className="flex items-center gap-3 text-xl">
                <div className="w-12 h-12 bg-gold/20 rounded-full flex items-center justify-center">
                  <Users className="w-6 h-6 text-gold" />
                </div>
                <span className="text-gray-300">面接随時受付中</span>
              </div>
              <div className="flex items-center gap-3 text-xl">
                <div className="w-12 h-12 bg-gold/20 rounded-full flex items-center justify-center">
                  <Clock className="w-6 h-6 text-gold" />
                </div>
                <span className="text-gray-300">24時間対応</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 px-4 bg-black/80">
        <div className="max-w-6xl mx-auto">
          <motion.h2
            className="text-4xl md:text-6xl font-bold text-center mb-16 text-gold"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            Entry／応募方法
          </motion.h2>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Web Contact */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Card className="bg-black/40 border-gold/30 backdrop-blur-sm">
                <CardContent className="p-8">
                  <div className="mb-6">
                    <Badge className="bg-blue-600 text-white px-3 py-1 text-sm mb-4">Web応募</Badge>
                    <h3 className="text-2xl font-bold text-gold mb-2">24時間即時受付</h3>
                    <p className="text-gray-300">お問い合わせフォームまたはLINEよりご連絡ください</p>
                  </div>

                  <div className="space-y-4">
                    <Button className="w-full bg-gold/10 border border-gold/40 text-gold hover:bg-gold hover:text-black transition-all duration-300 flex items-center justify-between py-6">
                      <div className="flex items-center">
                        <FileText className="w-5 h-5 mr-3" />
                        お問い合わせフォーム
                      </div>
                      <Send className="w-5 h-5" />
                    </Button>

                    <Button
                      className="w-full bg-green-600/20 border border-green-500/40 text-green-400 hover:bg-green-600 hover:text-white transition-all duration-300 flex items-center justify-between py-6"
                      onClick={() => window.open('https://lin.ee/A7EOdNV', '_blank')}
                    >
                      <div className="flex items-center">
                        <MessageCircle className="w-5 h-5 mr-3" />
                        LINE
                      </div>
                      <Send className="w-5 h-5" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Phone Contact */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Card className="bg-black/40 border-gold/30 backdrop-blur-sm">
                <CardContent className="p-8">
                  <div className="mb-6">
                    <Badge className="bg-gold text-black px-3 py-1 text-sm mb-4">Tel応募</Badge>
                    <h3 className="text-2xl font-bold text-gold mb-2">お問い合わせだけでも大歓迎！</h3>
                    <p className="text-gray-300">担当／ジャングル東京 採用担当まで 24時間即時受付</p>
                  </div>

                  <div className="space-y-6">
                    <div className="flex items-center p-4 bg-gold/5 rounded-lg border border-gold/20">
                      <Phone className="w-6 h-6 text-gold mr-4" />
                      <div>
                        <p className="text-2xl font-bold text-gold">Tel.************</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Application Process */}
          <motion.div
            className="mt-16"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card className="bg-black/40 border-gold/30 backdrop-blur-sm">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gold mb-6 text-center">応募後の流れ</h3>
                <div className="text-gray-300 leading-relaxed space-y-4">
                  <p>※お電話が繋がらない場合は、お手数ですがメールフォームからお問い合わせをお願い致します。</p>
                  <p>※店舗時間により応募への回答をお時間いただく場合がございます。あらかじめご了承ください。</p>
                  <p>※少しでも気になった方はお気軽にご連絡ください♪</p>
                  <p className="mt-6 pt-4 border-t border-gold/20">
                    <span className="font-bold text-gold">WEB応募された方は、</span>
                    担当者よりお返事させて頂きますので、★返信希望日時★をご応募の際にお書きください。
                    <br />※日時によって、お手数をお掛けしないよう配慮がございます。あらかじめご了承のほどお願いよろしくください。
                    <br />どんなことでも親身につらせて頂きます♪不安なこと、どんなお悩み、どんなお話しか何いって何でも安心につまりして頂いことをご縁問に関わって頂ましてありがとうございます！
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default MaleRecruitment;