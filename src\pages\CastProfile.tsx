import React, { useState, useRef, useEffect, useLayoutEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { Button } from "@/components/ui/button";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import Footer from "@/components/Footer";
import { ArrowLeft, Instagram, Twitter, Heart, Calendar, Droplets, Coffee, MapPin, X, ZoomIn } from "lucide-react";
import { getCastMemberById, getOtherCastMembers } from "@/data/castData";

const CastProfile: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { language } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.1 });
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [mainImage, setMainImage] = useState<string>("");
  const [isImageLoading, setIsImageLoading] = useState<boolean>(false);

  // Get profile from centralized data
  const profile = getCastMemberById(parseInt(id || '1'));

  // Immediate scroll to top before any rendering happens
  useLayoutEffect(() => {
    window.scrollTo(0, 0);
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
  }, [id]);

  useEffect(() => {
    if (profile) {
      setMainImage(profile.image);
    }
  }, [profile]);

  // Scroll to top immediately when component mounts or ID changes
  useEffect(() => {
    // Immediate scroll to top without smooth behavior for better UX
    window.scrollTo({ top: 0, left: 0, behavior: 'instant' });
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
    
    // Additional safety to ensure we're at the top
    const scrollToTop = () => {
      if (window.pageYOffset > 0) {
        window.scrollTo(0, 0);
      }
    };
    
    // Use requestAnimationFrame to ensure this happens after render
    requestAnimationFrame(scrollToTop);
  }, [id]);

  useEffect(() => {
    if (selectedImage) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [selectedImage]);

  const handleIntelligentReturn = () => {
    const returnSource = sessionStorage.getItem('brillar-return-source');
    
    // Clear the return source after reading
    sessionStorage.removeItem('brillar-return-source');
    sessionStorage.setItem('brillar-internal-nav', 'true');
    
    if (returnSource === 'home-gallery') {
      // Set target section for home page to scroll to
      sessionStorage.setItem('brillar-return-target', 'gallery');
      // Return to home page
      navigate('/');
    } else if (returnSource === 'allcast') {
      // Return to all cast page
      navigate('/cast');
    } else {
      // Fallback: return to all cast page if source is unknown
      navigate('/cast');
    }
  };

  if (!profile) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-3xl font-serif text-gold mb-4">Profile Not Found</h1>
          <Button onClick={handleIntelligentReturn} className="royal-button">
            {language === 'en' ? 'Return' : '戻る'}
          </Button>
        </div>
      </div>
    );
  }

  // Get other cast members for the carousel (exclude current profile)
  const otherCastMembers = getOtherCastMembers(profile.id);

  const handleThumbnailClick = (image: string) => {
    if (image !== mainImage) {
      setIsImageLoading(true);
      setTimeout(() => {
        setMainImage(image);
        setIsImageLoading(false);
      }, 300);
    }
  };

  const handleImageZoom = (image: string) => {
    setSelectedImage(image);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900/50 to-black" style={{ scrollBehavior: 'auto' }}>
      {/* Header */}
      <div className="sticky top-0 z-10 bg-black/80 backdrop-blur-sm border-b border-gold/20">
        <div className="max-w-7xl mx-auto px-4 md:px-8 py-4 flex items-center justify-between">
          <Button
            onClick={handleIntelligentReturn}
            variant="ghost"
            className="text-gold hover:text-gold-light hover:bg-gold/10 transition-all duration-300"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            {language === 'en' ? 'Return' : '戻る'}
          </Button>
          
          <h1 className="text-xl md:text-2xl font-serif text-gold gold-glow">
            {language === 'en' ? profile.name : profile.nameJa}
          </h1>
          
          <div className="w-24"></div>
        </div>
      </div>

      {/* Main Content */}
      <div 
        ref={ref}
        className={`reveal ${isInView ? 'active' : ''}`}
      >
        {/* Hero Section with Profile Photo */}
        <div className="relative h-[60vh] md:h-[70vh] overflow-hidden">
          <img 
            src={profile.image}
            alt={profile.name}
            className="w-full h-full object-cover object-center"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black via-black/20 to-transparent"></div>
          
          {/* Profile Info Overlay */}
          <div className="absolute bottom-0 left-0 right-0 p-6 md:p-12">
            <div className="max-w-4xl mx-auto text-center" style={{ animationDelay: '0.2s' }}>
              <h1 className="text-4xl md:text-6xl font-serif text-gold mb-4 gold-glow">
                {language === 'en' ? profile.name : profile.nameJa}
              </h1>
              <h2 className="text-xl md:text-2xl text-gold/80 mb-2">
                {language === 'en' ? profile.nickname : profile.nicknameJa}
              </h2>
              <p className="text-lg text-gold/70 mb-6">
                {language === 'en' ? profile.birthday : profile.birthdayJa}
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto"></div>
            </div>
          </div>
        </div>

        {/* Main Portrait Section */}
        <div className="max-w-4xl mx-auto px-4 md:px-8 py-12" style={{ animationDelay: '0.3s' }}>
          <div className="text-center mb-8">
            <div className="relative inline-block group">
              <div 
                className="relative overflow-hidden rounded-2xl cursor-zoom-in transition-all duration-500"
                onClick={() => handleImageZoom(mainImage)}
              >
                {/* Loading overlay with luxury slide animation */}
                {isImageLoading && (
                  <div className="absolute inset-0 bg-black/40 backdrop-blur-sm rounded-2xl z-10 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gold/30 to-transparent animate-luxury-slide"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-gold font-serif text-lg tracking-widest">
                        {language === 'en' ? 'Loading...' : '読み込み中...'}
                      </div>
                    </div>
                  </div>
                )}
                
                <img
                  src={mainImage}
                  alt={profile.name}
                  className={`w-full max-w-md mx-auto h-[600px] object-cover object-center transition-all duration-700 group-hover:scale-110 shadow-2xl ${
                    isImageLoading ? 'opacity-70 scale-95' : 'opacity-100 scale-100'
                  }`}
                  style={{
                    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 30px rgba(212, 175, 55, 0.2)'
                  }}
                />
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                
                {/* Zoom overlay */}
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-center justify-center">
                  <div className="bg-black/50 backdrop-blur-sm rounded-full p-3 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                    <ZoomIn className="w-6 h-6 text-gold" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Image Thumbnails */}
          <div className="flex justify-center mb-12">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl">
              {profile.gallery.map((image, index) => (
                <div
                  key={index}
                  onClick={() => handleThumbnailClick(image)}
                  className={`relative aspect-[3/4] rounded-xl overflow-hidden cursor-pointer group transition-all duration-500 hover:scale-105 hover:shadow-xl ${
                    mainImage === image 
                      ? 'ring-2 ring-gold shadow-lg shadow-gold/30 scale-105' 
                      : 'hover:ring-1 hover:ring-gold/50'
                  }`}
                >
                  <img
                    src={image}
                    alt={`Gallery ${index + 1}`}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute inset-0 border-2 border-transparent group-hover:border-gold/30 rounded-xl transition-all duration-300"></div>
                  {mainImage === image && (
                    <div className="absolute inset-0 bg-gold/10 rounded-xl">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gold/20 to-transparent animate-royal-shimmer"></div>
                    </div>
                  )}
                  
                  {/* Selection indicator */}
                  {mainImage === image && (
                    <div className="absolute top-2 right-2 bg-gold text-black rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                      ✓
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bio Section */}
        <div className="max-w-4xl mx-auto px-4 md:px-8 py-12" style={{ animationDelay: '0.4s' }}>
          <div className="text-center mb-12">
            <p className="text-lg md:text-xl text-gold-light leading-relaxed max-w-3xl mx-auto">
              {language === 'en' ? profile.bio : profile.bioJa}
            </p>
          </div>

          {/* About Me Section */}
          <div className="max-w-2xl mx-auto mb-12">
            <div className="space-y-6">
              <h3 className="text-2xl font-serif text-gold mb-6 gold-glow text-center">
                {language === 'en' ? 'About Me' : '私について'}
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center gap-3 justify-center">
                  <Calendar className="w-5 h-5 text-gold" />
                  <span className="text-gold/70">
                    {language === 'en' ? 'Birthday:' : '誕生日:'} 
                  </span>
                  <span className="text-gold-light">
                    {language === 'en' ? profile.birthday : profile.birthdayJa}
                  </span>
                </div>
                
                {/* <div className="flex items-center gap-3 justify-center">
                  <Droplets className="w-5 h-5 text-gold" />
                  <span className="text-gold/70">
                    {language === 'en' ? 'Blood Type:' : '血液型:'} 
                  </span>
                  <span className="text-gold-light">{profile.bloodType}</span>
                </div> */}
                
                {/* <div className="flex items-center gap-3 justify-center">
                  <Heart className="w-5 h-5 text-gold" />
                  <span className="text-gold/70">
                    {language === 'en' ? 'Hobbies:' : '趣味:'} 
                  </span>
                  <span className="text-gold-light">
                    {language === 'en' ? profile.hobbies : profile.hobbiesJa}
                  </span>
                </div> */}
                
                {/* <div className="flex items-center gap-3 justify-center">
                  <Coffee className="w-5 h-5 text-gold" />
                  <span className="text-gold/70">
                    {language === 'en' ? 'Favorite Drink:' : '好きな飲み物:'} 
                  </span>
                  <span className="text-gold-light">
                    {language === 'en' ? profile.favoriteDrink : profile.favoriteDrinkJa}
                  </span>
                </div> */}
                
                <div className="flex items-center gap-3 justify-center">
                  <MapPin className="w-5 h-5 text-gold" />
                  <span className="text-gold/70">
                    {language === 'en' ? 'Dream Date:' : '理想のデート:'} 
                  </span>
                  <span className="text-gold-light">
                    {language === 'en' ? profile.dreamDate : profile.dreamDateJa}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Other Cast Members Carousel */}
      <div className="relative py-24 overflow-hidden">
        {/* Premium background */}
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/70 to-black"></div>
        <div className="absolute inset-0 opacity-10"
             style={{
               backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30l15-15v30l-15-15zm-15 0l-15-15v30l15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
             }} />
        
        <div className="max-w-7xl mx-auto px-4 md:px-8 relative z-10">
          <div className="text-center mb-16">
            <h3 className="text-4xl md:text-5xl font-serif text-gold mb-6 gold-glow">
              {language === 'en' ? 'Meet Our Other Stars' : '他のキャストに会う'}
            </h3>
            <div className="w-32 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-6"></div>
            <p className="text-gold/70 text-lg max-w-2xl mx-auto leading-relaxed">
              {language === 'en' 
                ? 'Discover more of our talented cast members who bring elegance and charm to every moment' 
                : '優雅さと魅力を持つ他の才能あるキャストメンバーを発見してください'
              }
            </p>
          </div>

          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full max-w-6xl mx-auto"
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              {otherCastMembers.map((castMember, index) => (
                <CarouselItem key={castMember.id} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                  <div 
                    className="group cursor-pointer h-full"
                    onClick={() => {
                      // When navigating to another cast member from CastProfile, preserve the original return source
                      const currentReturnSource = sessionStorage.getItem('brillar-return-source');
                      if (!currentReturnSource) {
                        // If no return source is set, default to allcast
                        sessionStorage.setItem('brillar-return-source', 'allcast');
                      }
                      sessionStorage.setItem('brillar-internal-nav', 'true');
                      navigate(`/cast/${castMember.id}`);
                    }}
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    {/* Premium Card Container */}
                    <div className="relative bg-gradient-to-br from-black/90 via-gray-900/80 to-black/90 rounded-2xl overflow-hidden border border-gold/20 transition-all duration-700 group-hover:border-gold/50 group-hover:shadow-[0_25px_60px_rgba(212,175,55,0.15)] group-hover:-translate-y-3 backdrop-blur-sm">
                      
                      {/* Image Section */}
                      <div className="relative overflow-hidden">
                        <img
                          src={castMember.image}
                          alt={castMember.name}
                          className="w-full h-72 object-cover object-center transition-all duration-700 group-hover:scale-110"
                        />
                        
                        {/* Premium gradient overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/30 to-transparent opacity-70 group-hover:opacity-50 transition-opacity duration-500"></div>
                        
                        {/* Premium shimmer effect */}
                        <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gold/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out"></div>
                        </div>

                        {/* Floating overlay content */}
                        <div className="absolute bottom-0 left-0 right-0 p-6 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-500">
                          <h4 className="text-gold font-serif text-2xl mb-2 gold-glow">
                            {language === 'en' ? castMember.name : castMember.nameJa}
                          </h4>
                          <p className="text-gold/90 text-lg font-medium mb-1">
                            {language === 'en' ? castMember.nickname : castMember.nicknameJa}
                          </p>
                        </div>

                        {/* Decorative corner elements */}
                        <div className="absolute top-3 left-3 w-6 h-6 border-l-2 border-t-2 border-gold/40 group-hover:border-gold/80 transition-colors duration-500"></div>
                        <div className="absolute top-3 right-3 w-6 h-6 border-r-2 border-t-2 border-gold/40 group-hover:border-gold/80 transition-colors duration-500"></div>
                      </div>

                      {/* Content Section */}
                      <div className="p-6 space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-gold/80 text-sm font-medium">
                            {language === 'en' ? 'Age:' : '年齢:'} {castMember.age}
                          </span>
                          <div className="flex space-x-2">
                            <Heart className="w-4 h-4 text-gold/60 group-hover:text-gold group-hover:scale-110 transition-all duration-300" />
                          </div>
                        </div>
                        
                        <p className="text-gray-300 text-sm line-clamp-2 group-hover:text-gold-light transition-colors duration-500 leading-relaxed">
                          {language === 'en' ? castMember.bio : castMember.bioJa}
                        </p>

                        {/* Premium Action Button */}
                        <div className="pt-4 border-t border-gold/20 group-hover:border-gold/40 transition-colors duration-500">
                          <Button 
                            className="w-full bg-gradient-to-r from-gold/10 to-gold/20 hover:from-gold/30 hover:to-gold/40 text-white border border-gold/40 hover:border-gold/70 transition-all duration-500 rounded-xl py-3 font-medium shadow-lg hover:shadow-xl backdrop-blur-sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Preserve the original return source when navigating to another cast member
                              const currentReturnSource = sessionStorage.getItem('brillar-return-source');
                              if (!currentReturnSource) {
                                sessionStorage.setItem('brillar-return-source', 'allcast');
                              }
                              sessionStorage.setItem('brillar-internal-nav', 'true');
                              navigate(`/cast/${castMember.id}`);
                            }}
                          >
                            <span className="relative z-10">
                              {language === 'en' ? 'View Profile' : 'プロフィールを見る'}
                            </span>
                          </Button>
                        </div>
                      </div>

                      {/* Premium hover border effect */}
                      <div className="absolute inset-0 rounded-2xl border-2 border-transparent group-hover:border-gold/30 transition-all duration-500 pointer-events-none"></div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            
            {/* Enhanced Navigation Buttons */}
            <CarouselPrevious className="bg-black/90 border-gold/40 text-gold hover:bg-gold/20 hover:text-white hover:border-gold/70 -left-12 lg:-left-16 w-12 h-12 backdrop-blur-sm transition-all duration-300 hover:scale-110" />
            <CarouselNext className="bg-black/90 border-gold/40 text-gold hover:bg-gold/20 hover:text-white hover:border-gold/70 -right-12 lg:-right-16 w-12 h-12 backdrop-blur-sm transition-all duration-300 hover:scale-110" />
          </Carousel>
        </div>
      </div>

      {/* Footer */}
      <Footer />

      {/* Enhanced Lightbox Modal */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black/95 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fade-in"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-5xl max-h-[95vh] w-full animate-scale-in">
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-4 right-4 z-10 bg-gold/20 hover:bg-gold hover:text-black text-gold w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 hover:rotate-90"
            >
              <X className="w-6 h-6" />
            </button>
            <img
              src={selectedImage}
              alt="Gallery Full Size"
              className="w-full h-full object-contain rounded-lg cursor-zoom-out"
              onClick={() => setSelectedImage(null)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default CastProfile;