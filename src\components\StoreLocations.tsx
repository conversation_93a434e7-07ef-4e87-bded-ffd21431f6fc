
import React, { useRef } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Phone } from "lucide-react";
import { useNavigate } from "react-router-dom";
import logo1 from "../images/logo.webp";

// Store data
const stores = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    nameEn: "BRILLAR",
    address: <>〒400-0032<br/>山梨県甲府市中央1-12-5五光ビル 4F</>,
    addressEn: <>〒400-0032<br/>山梨県甲府市中央1-12-5五光ビル 4F</>,
    phone: "************",
    logo: logo1,
    location: "https://maps.app.goo.gl/vz3YXcZbm7A7tsR57"
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON> ",
    nameEn: "LUCIA",
    address: <>〒400-0032 <br /> 山梨県甲府市中央1-6-4花月ビル1F</>,
    addressEn: <>5F Dogenzaka Center Building, 2-29-8 Dogenzaka, Shibuya-ku, Tokyo</>,
    phone: "************",
    logo: logo1,
    location: "https://maps.app.goo.gl/C67x8CHXZVTeZYdg7"
  },
  {
    id: 3,
    name: "VEGA ",
    nameEn: "VEGA",
    address: <>〒400-0032 <br /> 山梨県甲府市中央1-12-5五光ビル 1F</>,
    addressEn: <>〒400-0032 <br /> 山梨県甲府市中央1-12-5五光ビル 1F</>,
    phone: "03-5797-9999",
    logo: logo1,
    location: "https://maps.app.goo.gl/vz3YXcZbm7A7tsR57"
  },
  {
    id: 4,
    name: "LUMI ",
    nameEn: "LUMI ",
    address: <>〒400-0032 <br /> 山梨県甲府市中央1-12-4</>,
    addressEn: <>〒400-0032 <br /> 山梨県甲府市中央1-12-4</>,
    phone: "03-6380-5555",
    logo: logo1,
    location: "https://maps.app.goo.gl/vz3YXcZbm7A7tsR57"
  },
];

const StoreLocations: React.FC = () => {
  const { language } = useLanguage();
  const navigate = useNavigate();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.02 });

  const handlePhoneCall = (phone: string) => {
    window.location.href = `tel:${phone}`;
  };

  const handleStoreClick = (storeId: number) => {
    // Store the source section for return navigation
    sessionStorage.setItem('brillar-return-source', 'home-stores');
    sessionStorage.setItem('brillar-internal-nav', 'true');
    navigate(`/store/${storeId}`);
  };

  return (
    <section id="stores" className="py-24 px-4 md:px-8 relative overflow-hidden">
      {/* Royal background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black" />
      <div className="absolute top-0 left-0 w-full h-full opacity-5"
           style={{
             backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30l15-15v30l-15-15zm-15 0l-15-15v30l15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
           }} />
      
      <div 
        ref={ref}
        className={`max-w-4xl mx-auto reveal relative z-10 ${isInView ? 'active' : ''}`}
      >
        <div className="text-center mb-16">
          <h2 className="section-heading">
            {language === 'en' ? 'Store Locations' : '店舗一覧'}
          </h2>
        </div>

        {/* Store List */}
        <div className="space-y-6 mb-12">
          {stores.map((store, index) => (
            <Card 
              key={store.id}
              className="group cursor-pointer bg-gradient-to-r from-black/80 to-gray-900/80 border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:shadow-[0_15px_40px_rgba(212,175,55,0.25)] hover:-translate-y-1 backdrop-blur-sm"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  {/* Store Info */}
                  <div className="flex items-start gap-4 flex-1">
                    {/* Store Logo */}
                    <div className="flex-shrink-0 mt-1">
                      <img 
                        src={store.logo} 
                        alt={store.name}
                        className="w-12 h-12 md:w-16 md:h-16 object-contain filter drop-shadow-[0_0_5px_rgba(212,175,55,0.5)]"
                      />
                    </div>
                    
                    {/* Store Details */}
                    <div className="flex-1 min-w-0">
                      <h3 
                        className="text-xl md:text-2xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300 gold-glow mb-2 cursor-pointer"
                        onClick={() => handleStoreClick(store.id)}
                      >
                        {language === 'en' ? store.nameEn : store.name}
                      </h3>
                      
                      <div className="space-y-2">
                        <p className="text-gold-light leading-relaxed text-sm md:text-base break-words">
                          {language === 'en' ? store.addressEn : store.address}
                        </p>
                        
                        <div 
                          className="flex items-center gap-2 text-gold hover:text-gold-light transition-colors duration-200 cursor-pointer group/phone"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePhoneCall(store.phone);
                          }}
                        >
                          <Phone className="w-4 h-4 group-hover/phone:animate-pulse" />
                          <span className="text-sm md:text-base font-medium">
                            {store.phone}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Location Pin */}
                  <div className="flex items-center justify-end md:justify-center" onClick={() => window.open(store.location)}>
                    <div className="bg-gold/20 p-3 rounded-full border border-gold/30 group-hover:bg-gold/30 group-hover:border-gold/50 transition-all duration-300 group-hover:scale-110">
                      <MapPin className="w-6 h-6 text-gold group-hover:animate-bounce" />
                    </div>
                  </div>
                </div>
                
                {/* Decorative border */}
                <div className="mt-4 w-0 group-hover:w-full h-0.5 bg-gradient-to-r from-gold to-transparent transition-all duration-700"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        
      </div>
    </section>
  );
};

export default StoreLocations;
