import React, { useRef } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { Card, CardContent } from "@/components/ui/card";
import { Users, Clock3, Clock, Wine, Heart, CreditCard, Banknote, Wallet, Crown, Gem, UserCheck, UserPlus } from "lucide-react";
import { Store } from "@/data/storeData";

interface PricingTableProps {
  store?: Store;
}

const PricingTable: React.FC<PricingTableProps> = ({ store }) => {
  const { language } = useLanguage();
  const pricingRef = useRef<HTMLDivElement>(null);
  const isPricingInView = useIntersectionObserver(pricingRef, { threshold: 0.1 });

  // Check if this is LUMI store (ID 4), VEGA store (ID 3), or LUCIA store (ID 2)
  const isLumiStore = store?.id === 4;
  const isVegaStore = store?.id === 3;
  const isLuciaStore = store?.id === 2;

  // Render LUCIA-specific pricing
  if (isLuciaStore) {
    return (
      <section
        ref={pricingRef}
        className={`py-24 md:py-32 px-4 md:px-8 bg-secondary reveal ${isPricingInView ? 'active' : ''}`}
      >
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-serif text-gold text-center mb-16 md:mb-20 gold-glow">
            {language === 'en' ? 'System & Pricing' : 'システム・料金'}
          </h2>

          {/* LUCIA Main and VIP Pricing */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-16 mb-16">
            {/* Main Section */}
            <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
              <CardContent className="p-8 md:p-10">
                <div className="flex items-center justify-center gap-3 mb-8">
                  <Users className="w-8 h-8 text-gold" />
                  <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">
                    {language === 'en' ? 'Main' : '通常料金（Main）'}
                  </h3>
                </div>

                <div className="space-y-6">
                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                      <Clock3 className="w-5 h-5" />
                      1時間 1セット (60分)
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">1名様 (1 person)</span>
                        <span className="text-gold font-bold text-xl">¥5,500</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">2名様以上 (2+ people)</span>
                        <span className="text-gold font-bold text-xl">¥4,500</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                      <Clock className="w-5 h-5" />
                      延長料金 (Extension Fee)
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">30分 (30 minutes)</span>
                        <span className="text-gold font-bold text-xl">¥3,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">60分 (60 minutes)</span>
                        <span className="text-gold font-bold text-xl">SET料金と同額</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-4 border border-gold/20">
                    <p className="text-center text-gold-light">
                      <span className="font-semibold text-gold">TAX 10%</span> + <span className="font-semibold text-gold">S.C 15%</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* VIP Section */}
            <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
              <CardContent className="p-8 md:p-10">
                <div className="flex items-center justify-center gap-3 mb-8">
                  <Gem className="w-8 h-8 text-gold" />
                  <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">
                    {language === 'en' ? 'VIP' : 'VIP料金'}
                  </h3>
                </div>

                <div className="space-y-6">
                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                      <Clock3 className="w-5 h-5" />
                      1時間 1セット (60分)
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">1名様 (1 person)</span>
                        <span className="text-gold font-bold text-xl">¥8,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">2名様以上 (2+ people)</span>
                        <span className="text-gold font-bold text-xl">¥7,000</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                      <Clock className="w-5 h-5" />
                      延長料金 (Extension Fee)
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">30分 (30 minutes)</span>
                        <span className="text-gold font-bold text-xl">¥4,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">60分 (60 minutes)</span>
                        <span className="text-gold font-bold text-xl">SET料金と同額</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-900/20 to-gold/10 rounded-xl p-4 border border-purple-500/30">
                    <p className="text-center text-gold-light mb-2">
                      <Wine className="w-5 h-5 inline mr-2" />
                      <span className="font-semibold text-purple-300">VIPフロアはボトルキープ制</span>
                    </p>
                    <p className="text-center text-gold-light text-sm">※自動延長になります</p>
                  </div>

                  <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-4 border border-gold/20">
                    <p className="text-center text-gold-light">
                      <span className="font-semibold text-gold">TAX 10%</span> + <span className="font-semibold text-gold">S.C 20%</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Additional Services Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-16 mb-16">
            {/* Drink Pricing */}
            <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
              <CardContent className="p-8 md:p-10">
                <div className="flex items-center justify-center gap-3 mb-8">
                  <Wine className="w-8 h-8 text-gold" />
                  <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">
                    {language === 'en' ? 'Drink Pricing' : 'ドリンク料金'}
                  </h3>
                </div>

                <div className="space-y-6">
                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">
                          {language === 'en' ? 'Cast Drink' : 'キャストドリンク'}
                        </span>
                        <span className="text-gold font-bold text-xl">¥1,300</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">
                          {language === 'en' ? 'Luge Drink' : 'ルジェドリンク'}
                        </span>
                        <span className="text-gold font-bold text-xl">¥2,000</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Nomination & Companion Services */}
            <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
              <CardContent className="p-8 md:p-10">
                <div className="flex items-center justify-center gap-3 mb-8">
                  <Heart className="w-8 h-8 text-gold" />
                  <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">
                    {language === 'en' ? 'Special Services' : '指名・同伴料金'}
                  </h3>
                </div>

                <div className="space-y-6">
                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">
                          {language === 'en' ? 'Main Nomination' : '本指名'}
                        </span>
                        <span className="text-gold font-bold text-xl">¥2,500</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">
                          {language === 'en' ? 'Floor Nomination' : '場内指名'}
                        </span>
                        <span className="text-gold font-bold text-xl">¥2,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">
                          {language === 'en' ? 'Companion Service' : '同伴'}
                        </span>
                        <span className="text-gold font-bold text-xl">¥5,000</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-4 border border-gold/20">
                    <p className="text-center text-gold-light text-sm">
                      {language === 'en'
                        ? '※Half of nomination fee charged per extension'
                        : '※延長ごとに指名料金の半額頂きます'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Methods */}
          <div className="text-center">
            <h3 className="text-2xl md:text-3xl font-serif text-gold mb-8 gold-glow">
              {language === 'en' ? 'Accepted Payment Methods' : 'お支払い方法'}
            </h3>
            <div className="flex flex-wrap justify-center items-center gap-6 md:gap-10">
              {/* Credit Cards */}
              <div className="group flex items-center gap-3 bg-gradient-to-r from-black/60 to-gray-900/60 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:scale-110">
                <CreditCard className="w-8 md:w-10 h-8 md:h-10 text-gold group-hover:animate-bounce" />
                <span className="text-gold-light text-sm md:text-base font-medium">Credit Cards</span>
              </div>

              {/* Cash */}
              <div className="group flex items-center gap-3 bg-gradient-to-r from-black/60 to-gray-900/60 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:scale-110">
                <Banknote className="w-8 md:w-10 h-8 md:h-10 text-gold group-hover:animate-bounce" />
                <span className="text-gold-light text-sm md:text-base font-medium">Cash</span>
              </div>
            </div>

            {/* Supported Card Types */}
            <div className="mt-8">
              <p className="text-gold-light text-sm md:text-base">
                VISA / MasterCard / AMEX / JCB
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Render VEGA-specific pricing
  if (isVegaStore) {
    return (
      <section
        ref={pricingRef}
        className={`py-24 md:py-32 px-4 md:px-8 bg-secondary reveal ${isPricingInView ? 'active' : ''}`}
      >
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-serif text-gold text-center mb-16 md:mb-20 gold-glow">
            {language === 'en' ? 'System & Pricing' : 'システム・料金'}
          </h2>

          {/* VEGA All-You-Can-Drink Pricing */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-16 mb-16">
            {/* All-You-Can-Drink Plan */}
            <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
              <CardContent className="p-8 md:p-10">
                <div className="flex items-center justify-center gap-3 mb-8">
                  <Wine className="w-8 h-8 text-gold" />
                  <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">
                    {language === 'en' ? 'All-You-Can-Drink' : '飲み放題メニュー'}
                  </h3>
                </div>

                <div className="space-y-6">
                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                      <Users className="w-5 h-5" />
                      {language === 'en' ? 'Group Pricing' : 'ご利用人数料金（お一人様）'}
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">1名様 (1 person)</span>
                        <span className="text-gold font-bold text-xl">¥3,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">2名様 (2 people)</span>
                        <span className="text-gold font-bold text-xl">¥2,500</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                      <Heart className="w-5 h-5" />
                      {language === 'en' ? 'Snack Platter' : 'お菓子盛り合わせ'}
                    </h4>
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">
                        {language === 'en' ? 'Assorted Snacks' : 'お菓子盛り合わせ'}
                      </span>
                      <span className="text-gold font-bold text-xl">¥1,000</span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-4 border border-gold/20">
                    <p className="text-center text-gold-light">
                      <span className="font-semibold text-gold">TAX</span> + <span className="font-semibold text-gold">サービス料 15%</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Drink Menu */}
            <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
              <CardContent className="p-8 md:p-10">
                <div className="flex items-center justify-center gap-3 mb-8">
                  <Wine className="w-8 h-8 text-gold" />
                  <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">
                    {language === 'en' ? 'Drink Menu' : 'ドリンクメニュー'}
                  </h3>
                </div>

                <div className="space-y-6">
                  {/* Cocktails */}
                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4">
                      {language === 'en' ? 'Cocktails' : 'カクテル'}
                    </h4>
                    <div className="grid grid-cols-2 gap-2 text-gold-light text-sm">
                      <span>ピーチ (Peach)</span>
                      <span>カシス (Cassis)</span>
                      <span>マリブ (Malibu)</span>
                    </div>
                  </div>

                  {/* Soft Drinks */}
                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4">
                      {language === 'en' ? 'Soft Drinks' : 'ソフトドリンク'}
                    </h4>
                    <div className="grid grid-cols-2 gap-2 text-gold-light text-sm">
                      <span>ウーロン茶 (Oolong Tea)</span>
                      <span>緑茶 (Green Tea)</span>
                      <span>オレンジジュース</span>
                      <span>リンゴジュース</span>
                      <span>カルピス (Calpis)</span>
                      <span>麦茶 (Barley Tea)</span>
                    </div>
                  </div>

                  {/* Alcohol */}
                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4">
                      {language === 'en' ? 'Alcohol' : 'その他（アルコール）'}
                    </h4>
                    <div className="grid grid-cols-2 gap-2 text-gold-light text-sm">
                      <span>ハイボール (Highball)</span>
                      <span>ウーロンハイ</span>
                      <span>緑茶ハイ</span>
                      <span>麦焼酎 (Barley Shochu)</span>
                      <span>梅酒 (Plum Wine)</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Methods */}
          <div className="text-center">
            <h3 className="text-2xl md:text-3xl font-serif text-gold mb-8 gold-glow">
              {language === 'en' ? 'Accepted Payment Methods' : 'お支払い方法'}
            </h3>
            <div className="flex flex-wrap justify-center items-center gap-6 md:gap-10">
              {/* Credit Cards */}
              <div className="group flex items-center gap-3 bg-gradient-to-r from-black/60 to-gray-900/60 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:scale-110">
                <CreditCard className="w-8 md:w-10 h-8 md:h-10 text-gold group-hover:animate-bounce" />
                <span className="text-gold-light text-sm md:text-base font-medium">Credit Cards</span>
              </div>

              {/* Cash */}
              <div className="group flex items-center gap-3 bg-gradient-to-r from-black/60 to-gray-900/60 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:scale-110">
                <Banknote className="w-8 md:w-10 h-8 md:h-10 text-gold group-hover:animate-bounce" />
                <span className="text-gold-light text-sm md:text-base font-medium">Cash</span>
              </div>
            </div>

            {/* Supported Card Types */}
            <div className="mt-8">
              <p className="text-gold-light text-sm md:text-base">
                VISA / MasterCard / AMEX / JCB
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Render LUMI-specific pricing
  if (isLumiStore) {
    return (
      <section
        ref={pricingRef}
        className={`py-24 md:py-32 px-4 md:px-8 bg-secondary reveal ${isPricingInView ? 'active' : ''}`}
      >
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl md:text-5xl font-serif text-gold text-center mb-16 md:mb-20 gold-glow">
            {language === 'en' ? 'System & Pricing' : 'システム・料金'}
          </h2>

          {/* LUMI Unlimited Pricing */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-16 mb-16">
            {/* Unlimited Plan */}
            <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
              <CardContent className="p-8 md:p-10">
                <div className="flex items-center justify-center gap-3 mb-8">
                  <Users className="w-8 h-8 text-gold" />
                  <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">
                    {language === 'en' ? 'Unlimited Plan' : '料金（無制限）'}
                  </h3>
                </div>

                <div className="space-y-6">
                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                      <Users className="w-5 h-5" />
                      {language === 'en' ? 'Group Pricing' : 'ご利用人数料金（お一人様）'}
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">1名様 (1 person)</span>
                        <span className="text-gold font-bold text-xl">¥10,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">2名様 (2 people)</span>
                        <span className="text-gold font-bold text-xl">¥9,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">3名様 (3 people)</span>
                        <span className="text-gold font-bold text-xl">¥8,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">4名様 (4 people)</span>
                        <span className="text-gold font-bold text-xl">¥7,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">5名様以上 (5+ people)</span>
                        <span className="text-gold font-bold text-xl">¥6,000</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-900/20 to-gold/10 rounded-xl p-4 border border-purple-500/30">
                    <p className="text-center text-gold-light mb-2">
                      <Wine className="w-5 h-5 inline mr-2" />
                      <span className="font-semibold text-purple-300">
                        {language === 'en' ? 'Bottle Keep System' : 'ボトルキープ制'}
                      </span>
                    </p>
                    <p className="text-center text-gold-light text-sm">
                      {language === 'en' ? 'Keep period: 3 months from last visit' : '※ボトルキープ期間は最終来店日より3ヶ月とさせていただきます'}
                    </p>
                    <p className="text-center text-gold-light text-sm mt-1">
                      {language === 'en' ? '※No all-you-can-drink available' : '※飲み放題はございません'}
                    </p>
                  </div>

                  <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-4 border border-gold/20">
                    <p className="text-center text-gold-light">
                      <span className="font-semibold text-gold">TAX 10%</span> + <span className="font-semibold text-gold">S.C 10%</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Services */}
            <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
              <CardContent className="p-8 md:p-10">
                <div className="flex items-center justify-center gap-3 mb-8">
                  <Heart className="w-8 h-8 text-gold" />
                  <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">
                    {language === 'en' ? 'Additional Services' : '指名・リクエスト・同伴料金'}
                  </h3>
                </div>

                <div className="space-y-6">
                  <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                    <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                      <UserCheck className="w-5 h-5" />
                      {language === 'en' ? 'Special Services' : 'スペシャルサービス'}
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">
                          {language === 'en' ? 'Main Nomination' : '本指名'}
                        </span>
                        <span className="text-gold font-bold text-xl">¥2,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">
                          {language === 'en' ? 'Request' : 'リクエスト'}
                        </span>
                        <span className="text-gold font-bold text-xl">¥2,000</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gold-light">
                          {language === 'en' ? 'Companion Service' : '同伴'}
                        </span>
                        <span className="text-gold font-bold text-xl">¥5,000</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-6 border border-gold/20">
                    <div className="text-center">
                      <UserPlus className="w-8 h-8 text-gold mx-auto mb-3" />
                      <h4 className="text-gold font-medium text-lg mb-2">
                        {language === 'en' ? 'Premium Experience' : 'プレミアム体験'}
                      </h4>
                      <p className="text-gold-light text-sm">
                        {language === 'en'
                          ? 'Personalized service with your preferred cast member'
                          : 'お気に入りのキャストとの特別なひととき'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Methods */}
          <div className="text-center">
            <h3 className="text-2xl md:text-3xl font-serif text-gold mb-8 gold-glow">
              {language === 'en' ? 'Accepted Payment Methods' : 'お支払い方法'}
            </h3>
            <div className="flex flex-wrap justify-center items-center gap-6 md:gap-10">
              {/* Credit Cards */}
              <div className="group flex items-center gap-3 bg-gradient-to-r from-black/60 to-gray-900/60 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:scale-110">
                <CreditCard className="w-8 md:w-10 h-8 md:h-10 text-gold group-hover:animate-bounce" />
                <span className="text-gold-light text-sm md:text-base font-medium">Credit Cards</span>
              </div>

              {/* Cash */}
              <div className="group flex items-center gap-3 bg-gradient-to-r from-black/60 to-gray-900/60 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:scale-110">
                <Banknote className="w-8 md:w-10 h-8 md:h-10 text-gold group-hover:animate-bounce" />
                <span className="text-gold-light text-sm md:text-base font-medium">Cash</span>
              </div>
            </div>

            {/* Supported Card Types */}
            <div className="mt-8">
              <p className="text-gold-light text-sm md:text-base">
                VISA / MasterCard / AMEX / JCB
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  // Default pricing for other stores
  return (
    <section
      ref={pricingRef}
      className={`py-24 md:py-32 px-4 md:px-8 bg-secondary reveal ${isPricingInView ? 'active' : ''}`}
    >
      <div className="max-w-7xl mx-auto">
        <h2 className="text-4xl md:text-5xl font-serif text-gold text-center mb-16 md:mb-20 gold-glow">
          {language === 'en' ? 'System & Pricing' : 'システム・料金'}
        </h2>

        {/* Main Pricing Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-16 mb-16">
          {/* Main Section */}
          <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
            <CardContent className="p-8 md:p-10">
              <div className="flex items-center justify-center gap-3 mb-8">
                <Users className="w-8 h-8 text-gold" />
                <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">Main</h3>
              </div>

              <div className="space-y-6">
                <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                  <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                    <Clock3 className="w-5 h-5" />
                    1時間 1セット
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">1名様 (1 person)</span>
                      <span className="text-gold font-bold text-xl">¥6,500</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">2名様以上 (2+ people)</span>
                      <span className="text-gold font-bold text-xl">¥5,500</span>
                    </div>
                  </div>
                </div>

                <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                  <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    延長料金 (Extension Fee)
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">30分 (30 minutes)</span>
                      <span className="text-gold font-bold text-xl">¥3,500</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">60分 (60 minutes)</span>
                      <span className="text-gold font-bold text-xl">SET料金と同額</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-4 border border-gold/20">
                  <p className="text-center text-gold-light">
                    <span className="font-semibold text-gold">TAX 10%</span> + <span className="font-semibold text-gold">S.C 15%</span>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Royal vip Section */}
          <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
            <CardContent className="p-8 md:p-10">
              <div className="flex items-center justify-center gap-3 mb-8">
                <Gem className="w-8 h-8 text-gold" />
                <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">VIP</h3>
              </div>

              <div className="space-y-6">
                <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                  <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                    <Clock3 className="w-5 h-5" />

                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">1名様 (1 person)</span>
                      <span className="text-gold font-bold text-xl">¥8,500</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">2名様以上 (2+ people)</span>
                      <span className="text-gold font-bold text-xl">¥7,500</span>
                    </div>
                  </div>
                </div>

                <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                  <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    延長料金 (Extension Fee)
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">30分 (30 minutes)</span>
                      <span className="text-gold font-bold text-xl">¥4,500</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">60分 (60 minutes)</span>
                      <span className="text-gold font-bold text-xl">SET料金と同額</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-900/20 to-gold/10 rounded-xl p-4 border border-purple-500/30">
                  <p className="text-center text-gold-light mb-2">
                    <Wine className="w-5 h-5 inline mr-2" />
                    <span className="font-semibold text-purple-300">VIPフロアはボトルキープ制</span>
                  </p>
                  <p className="text-center text-gold-light text-sm">(自動延長)</p>
                </div>

                <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-4 border border-gold/20">
                  <p className="text-center text-gold-light">
                    <span className="font-semibold text-gold">TAX 10%</span> + <span className="font-semibold text-gold">S.C 20%</span>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card className="group bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/30 hover:border-gold/60 transition-all duration-700 transform hover:scale-[1.02] hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)]">
            <CardContent className="p-8 md:p-10">
              <div className="flex items-center justify-center gap-3 mb-8">
                <Crown className="w-8 h-8 text-gold" />
                <h3 className="text-3xl md:text-4xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300">Royal VIP</h3>
              </div>

              <div className="space-y-6">
                <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                  <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                    <Clock3 className="w-5 h-5" />

                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">1名様 (1 person)</span>
                      <span className="text-gold font-bold text-xl">¥30,000</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">2名様以上 (2+ people)</span>
                      <span className="text-gold font-bold text-xl">¥15,000</span>
                    </div>
                  </div>
                </div>

                <div className="bg-black/40 rounded-xl p-6 border border-gold/10 hover:border-gold/30 transition-all duration-300">
                  <h4 className="text-gold font-medium text-lg mb-4 flex items-center gap-2">
                    <Clock className="w-5 h-5" />
                    延長料金 (Extension Fee)
                  </h4>
                  <div className="space-y-3">
                    <div>
                      <h4><span className="text-gold-light">30分 (30 minutes)</span></h4>
                      <div className="pl-5">
                        <div className="flex justify-between items-center">
                          <span className="text-gold-light">1 名様</span>

                          <span className="text-gold font-bold text-xl">¥15,000</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gold-light">2 名様以上</span>

                          <span className="text-gold font-bold text-xl">¥8,000</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gold-light">60分 (60 minutes)</span>
                      <span className="text-gold font-bold text-xl">SET料金と同額</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-900/20 to-gold/10 rounded-xl p-4 border border-purple-500/30">
                  <p className="text-center text-gold-light mb-2">
                    <Wine className="w-5 h-5 inline mr-2" />
                    <span className="font-semibold text-purple-300">VIPフロアはボトルキープ制</span>
                  </p>
                  <p className="text-center text-gold-light text-sm">(自動延長)</p>
                </div>

                <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-4 border border-gold/20">
                  <p className="text-center text-gold-light">
                    <span className="font-semibold text-gold">TAX 10%</span> + <span className="font-semibold text-gold">S.C 20%</span>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>



        {/* Payment Methods */}
        <div className="text-center">
          <h3 className="text-2xl md:text-3xl font-serif text-gold mb-8 gold-glow">
            {language === 'en' ? 'Accepted Payment Methods' : 'お支払い方法'}
          </h3>
          <div className="flex flex-wrap justify-center items-center gap-6 md:gap-10">
            {/* Credit Cards */}
            <div className="group flex items-center gap-3 bg-gradient-to-r from-black/60 to-gray-900/60 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:scale-110">
              <CreditCard className="w-8 md:w-10 h-8 md:h-10 text-gold group-hover:animate-bounce" />
              <span className="text-gold-light text-sm md:text-base font-medium">Credit Cards</span>
            </div>

            {/* Cash */}
            <div className="group flex items-center gap-3 bg-gradient-to-r from-black/60 to-gray-900/60 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:scale-110">
              <Banknote className="w-8 md:w-10 h-8 md:h-10 text-gold group-hover:animate-bounce" />
              <span className="text-gold-light text-sm md:text-base font-medium">Cash</span>
            </div>
          </div>

          {/* Supported Card Types */}
          <div className="mt-8">
            <p className="text-gold-light text-sm md:text-base">
              VISA / MasterCard / AMEX / JCB
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingTable;
