import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Star, Users, Music } from 'lucide-react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import shop1 from '../images/BRILLAR.webp';
import shop2 from '../images/lucia.webp';
import shop3 from '../images/vega.webp';
import shop4 from '../images/lumi.webp';

const shopData = [
  {
    id: 1,
    name: "BRILLAR",
    location: "渋谷区道玄坂",
    address: "東京都渋谷区道玄坂2-25-17 コクサイビル 3F",
    description: "2023年12月13日、甲府市にグランドオープン！ 山梨県甲府市に誕生した人気の大型店舗で、優雅なひとときをお届けします。 敷地面積は圧巻の100坪。接待や団体利用にも最適な、ステキな空間が広がっています！ アジアンテイストの豪華な内装と、厳選されたステキなキャストが至福の時間を演出します。上質なプライベートシーンはもちろん、接待などビジネスの場においても幅広くご利用頂けます。団体利用・接待用個室完備！ 落ち着いた雰囲気の中で、最上級のサービス・極上の空間・甘美なキャストと供に至福の時間をお過ごし下さい。",
    features: [
      { name: "VIPルーム完備", icon: Star },
      { name: "プレミアムカラオケ", icon: Music },
      { name: "専属シェフ料理", icon: Users }
    ],
    hours: "20:00 - 6:00",
    phone: "03-3461-8888",
    rating: "★★★★★",
    capacity: "50名様",
    image: shop1
  },
  {
    id: 2,
    name: "LUCIA",
    location: "渋谷区道玄坂",
    address: "東京都渋谷区道玄坂2-29-8 道玄坂センタービル 5F",
    description: "アットホームな雰囲気で親しみやすい接客を心掛けています。初回のお客様も安心してお楽しみいただける温かい空間です。リラックスした時間をお過ごしください。",
    features: [
      { name: "初心者歓迎", icon: Users },
      { name: "アットホーム", icon: Star },
      { name: "リーズナブル", icon: Music }
    ],
    hours: "20:00 - 5:00",
    phone: "03-3461-7777",
    rating: "★★★★☆",
    capacity: "35名様",
    image: shop2
  },
  {
    id: 3,
    name: "VEGA",
    location: "港区六本木",
    address: "東京都港区六本木3-15-24 ウイン六本木ビル 4F",
    description: "六本木の夜景を一望できる最上級の空間。特別な夜をお約束する極上のサービスをご提供。洗練された大人の社交場として、忘れられない体験をお届けします。",
    features: [
      { name: "夜景一望", icon: Star },
      { name: "極上サービス", icon: Users },
      { name: "完全個室", icon: Music }
    ],
    hours: "21:00 - 6:00",
    phone: "03-5797-9999",
    rating: "★★★★★",
    capacity: "60名様",
    image: shop3
  },
  {
    id: 4,
    name: "LUMI",
    location: "新宿区歌舞伎町",
    address: "東京都新宿区歌舞伎町1-6-2 第2東亜会館 6F",
    description: "歌舞伎町の中心で展開する会員制の特別店舗。厳選されたキャストによる至高のエンターテイメント。究極のプライベート空間で、最高級のおもてなしを体験してください。",
    features: [
      { name: "会員制", icon: Star },
      { name: "厳選キャスト", icon: Users },
      { name: "至高体験", icon: Music }
    ],
    hours: "21:00 - 7:00",
    phone: "03-6380-5555",
    rating: "★★★★★",
    capacity: "40名様",
    image: shop4
  }
];

const ShopOverview = () => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useIntersectionObserver(sectionRef, { threshold: 0.02 });

  const plugin = useRef(
    Autoplay({ delay: 60000, stopOnInteraction: true })
  );

  useEffect(() => {
    if (!api) {
      return;
    }

    setCurrent(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);

  return (
    <section ref={sectionRef} className="relative py-20 overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
      
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full opacity-5"
           style={{
             backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30l15-15v30l-15-15zm-15 0l-15-15v30l15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
           }} />

      <div
        className={`max-w-7xl mx-auto px-4 md:px-8 relative z-10 transition-opacity duration-1000 ease-out ${
          isInView ? 'opacity-100' : 'opacity-0'
        }`}
      >
        {/* Section Title */}
        <div className="text-center mb-16">
          <h2 className="section-heading">店舗紹介</h2>
          <p className="text-gold-light text-lg font-sans-jp mt-4">
            全4店舗で皆様をお待ちしております
          </p>
        </div>

        <Carousel
          setApi={setApi}
          plugins={[plugin.current]}
          className="w-full"
          onMouseEnter={plugin.current.stop}
          onMouseLeave={plugin.current.reset}
        >
          <CarouselContent>
            {shopData.map((shop, index) => (
              <CarouselItem key={shop.id}>
                {/* Split Screen Layout */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                  {/* Left Side - Image */}
                  <div className="relative group">
                    <Link to={`/store/${shop.id}`} className="block">
                      <div className="aspect-[4/3] rounded-3xl overflow-hidden shadow-2xl border border-gold/20 cursor-pointer">
                        <img
                          src={shop.image}
                          alt={shop.name}
                          loading="lazy"
                          decoding="async"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />

                        {/* Shop Name Overlay */}
                        <div className="absolute bottom-6 left-6 right-6">
                          <h3 className="text-2xl md:text-3xl font-bold text-white font-serif gold-glow">
                            {shop.name}
                          </h3>
                          <p className="text-gold-light font-sans-jp mt-1">
                            {shop.location}
                          </p>
                        </div>
                      </div>
                    </Link>
                  </div>

                  {/* Right Side - Information Card */}
                  <div className="space-y-4">
                    <div
                      className="relative bg-gradient-to-br from-black/70 via-gray-900/80 to-black/70 p-6 rounded-3xl border border-gold/30 backdrop-blur-xl shadow-2xl"
                      style={{
                        background: 'linear-gradient(135deg, rgba(0,0,0,0.85) 0%, rgba(20,20,20,0.9) 50%, rgba(0,0,0,0.85) 100%)',
                        backdropFilter: 'blur(20px)',
                        boxShadow: '0 20px 40px rgba(0,0,0,0.5), 0 0 30px rgba(212, 175, 55, 0.1), inset 0 1px 0 rgba(212, 175, 55, 0.2)'
                      }}
                    >
                      
                      {/* Decorative Golden Border */}
                      <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-gold/20 via-transparent to-gold/20 p-[1px]">
                        <div className="h-full w-full rounded-3xl bg-transparent" />
                      </div>

                      {/* Shop Title with Rating */}
                      <div className="relative mb-4">
                        <h3 className="text-2xl md:text-3xl font-bold text-gold mb-2 font-serif leading-tight">
                          {shop.name}
                        </h3>
                        <div className="flex items-center gap-3 mb-2">
                          <span className="text-gold text-sm">{shop.rating}</span>
                        </div>
                        <div className="w-16 h-[1px] bg-gradient-to-r from-gold to-gold-light rounded-full"></div>
                      </div>
                      
                      {/* Description */}
                      <p className="text-gray-200 text-sm leading-relaxed mb-4 font-sans-jp line-clamp-3">
                        {shop.description}
                      </p>

                      {/* Restaurant Menu Pricing */}
                      <div className="mb-4">
                        <h4 className="text-gold text-sm font-semibold mb-4 font-serif">レストランメニュー料金</h4>
                        
                        {/* Main Pricing */}
                        <div className="mb-4">
                          <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-4 border border-gold/30 mb-3">
                            <h5 className="text-gold font-semibold text-sm mb-3 font-serif flex items-center gap-2">
                              <Star className="w-4 h-4" />
                              Main
                            </h5>
                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-gray-200 text-xs font-sans-jp">1時間 1セット (1 hour, 1 set)</span>
                                <span className="text-gold font-bold text-sm">¥7,000</span>
                              </div>
                              <div className="w-full h-px bg-gradient-to-r from-transparent via-gold/30 to-transparent"></div>
                              <div className="flex justify-between items-center">
                                <span className="text-gray-200 text-xs font-sans-jp">1名様 (1 person)</span>
                                <span className="text-gold font-bold text-sm">¥7,000</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-gray-200 text-xs font-sans-jp">2名様以上 (2 or more people)</span>
                                <span className="text-gold font-bold text-sm">¥5,500</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* VIP Pricing */}
                        <div className="mb-4">
                          <div className="bg-gradient-to-r from-gold/20 to-gold/10 rounded-xl p-4 border border-gold/40 relative overflow-hidden">
                            <div className="absolute top-0 right-0 bg-gold text-black text-xs px-2 py-1 rounded-bl-lg font-bold">
                              VIP
                            </div>
                            <h5 className="text-gold font-semibold text-sm mb-3 font-serif flex items-center gap-2">
                              <Users className="w-4 h-4" />
                              VIP Package
                            </h5>
                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-gray-200 text-xs font-sans-jp">1時間 1セット 60分 (1 hour, 1 set, 60 minutes)</span>
                              </div>
                              <div className="w-full h-px bg-gradient-to-r from-transparent via-gold/40 to-transparent"></div>
                              <div className="flex justify-between items-center">
                                <span className="text-gray-200 text-xs font-sans-jp">1名様 (1 person)</span>
                                <span className="text-gold font-bold text-sm">¥8,500</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-gray-200 text-xs font-sans-jp">2名様以上 (2 or more people)</span>
                                <span className="text-gold font-bold text-sm">¥7,500</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Call to Action */}
                      <div className="mt-4 pt-4 border-t border-gold/20 relative z-10">
                        <Link
                          to={`/store/${shop.id}`}
                          className="block w-full bg-gradient-to-r from-gold to-gold-light text-black font-bold py-3 px-4 rounded-xl hover:from-gold-light hover:to-gold transition-all duration-300 hover:scale-[1.02] hover:shadow-lg hover:shadow-gold/30 font-serif text-sm text-center relative z-20"
                        >
                          詳細を見る
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>

        {/* Navigation Dots */}
        <div className="flex justify-center space-x-3 mt-12">
          {shopData.map((_, index) => (
            <button
              key={index}
              onClick={() => api?.scrollTo(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === current
                  ? 'bg-gold scale-125 shadow-lg shadow-gold/50'
                  : 'bg-gray-600 hover:bg-gold/60'
              }`}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default React.memo(ShopOverview);
