
import React, { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLanguage } from "@/hooks/use-language";
import { useNavigate } from "react-router-dom";

interface CircularDropdownMenuProps {
  onSelectItem: (section: string) => void;
}

const CircularDropdownMenu: React.FC<CircularDropdownMenuProps> = ({ onSelectItem }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { t, language, setLanguage } = useLanguage();
  const navigate = useNavigate();

  const handleSelectItem = (section: string) => {
    if (section === "home") {
      sessionStorage.setItem('brillar-internal-nav', 'true');
      navigate("/");
    } else if (section === "cast-link") {
      sessionStorage.setItem('brillar-internal-nav', 'true');
      navigate("/cast");
    } else {
      onSelectItem(section);
    }
    setIsOpen(false);
  };

  const toggleLanguage = () => {
    setLanguage(language === 'ja' ? 'en' : 'ja');
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <button 
          className="w-7 h-7 md:w-9 md:h-9 rounded-full bg-transparent border border-gold hover:border-gold-light flex items-center justify-center transition-all duration-300 focus:outline-none gold-glow"
          aria-label="Menu"
        >
          <div className="w-2 h-2 md:w-2.5 md:h-2.5 relative">
            <span className={`absolute top-0 left-0 w-full h-0.5 bg-gold transform transition-all duration-300 ${isOpen ? 'rotate-45 translate-y-1' : ''}`}></span>
            <span className={`absolute top-1/2 left-0 w-full h-0.5 bg-gold transform transition-all duration-300 ${isOpen ? 'opacity-0' : ''}`}></span>
            <span className={`absolute bottom-0 left-0 w-full h-0.5 bg-gold transform transition-all duration-300 ${isOpen ? '-rotate-45 -translate-y-1' : ''}`}></span>
          </div>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="bg-transparent backdrop-blur-md border border-gold/50 p-2 min-w-[160px] rounded-none">
        {/* 1. Home */}
        <DropdownMenuItem 
          className="text-gold hover:text-gold-light hover:bg-transparent cursor-pointer py-2 px-4"
          onClick={() => handleSelectItem("home")}
        >
          Home
        </DropdownMenuItem>
        {/* 2. Cast Page */}
        <DropdownMenuItem 
          className="text-gold hover:text-gold-light hover:bg-transparent cursor-pointer py-2 px-4"
          onClick={() => handleSelectItem("cast-link")}
        >
          Cast Page
        </DropdownMenuItem>
        {/* 3. Career (Recruitment Banner section) */}
        <DropdownMenuItem 
          className="text-gold hover:text-gold-light hover:bg-transparent cursor-pointer py-2 px-4"
          onClick={() => handleSelectItem("recruitment")}
        >
          Career
        </DropdownMenuItem>
        {/* 4. Notice (NoticeBoard section) */}
        <DropdownMenuItem 
          className="text-gold hover:text-gold-light hover:bg-transparent cursor-pointer py-2 px-4"
          onClick={() => handleSelectItem("notice")}
        >
          Notice
        </DropdownMenuItem>
        {/* Language toggle remains last */}
        <DropdownMenuItem 
          className="text-gold hover:text-gold-light hover:bg-transparent cursor-pointer py-2 px-4"
          onClick={toggleLanguage}
        >
          {language === 'ja' ? 'EN' : '日本語'}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CircularDropdownMenu;
