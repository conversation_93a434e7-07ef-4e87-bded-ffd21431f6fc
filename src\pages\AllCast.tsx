
import React, { useRef, useEffect, useState } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ChevronLeft, ChevronRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { getAllCastMembers, getCastMembersByShop } from "@/data/castData";

const AllCast: React.FC = () => {
  const { t, language } = useLanguage();
  const navigate = useNavigate();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.1 });
  
  // Check if device is mobile
  const [isMobile, setIsMobile] = useState(false);
  
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Pagination and filter state
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedShop, setSelectedShop] = useState<string>('All');
  const itemsPerPage = 30;

  // Get cast members based on selected filter
  const getCastMembers = () => {
    if (selectedShop === 'All') {
      return getAllCastMembers();
    } else {
      return getCastMembersByShop(selectedShop);
    }
  };

  const filteredMembers = getCastMembers();

  // Calculate pagination
  const totalPages = Math.ceil(filteredMembers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentMembers = filteredMembers.slice(startIndex, endIndex);



  // Scroll to top when component mounts, page changes, or filter changes
  useEffect(() => {
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
  }, [currentPage, selectedShop]);

  // Handle shop filter change
  const handleShopFilter = (shop: string) => {
    setSelectedShop(shop);
    setCurrentPage(1); // Reset to first page when changing filters
  };

  const handleCastClick = (castId: number) => {
    // Store the source page for return navigation
    sessionStorage.setItem('brillar-return-source', 'allcast');
    sessionStorage.setItem('brillar-internal-nav', 'true');
    navigate(`/cast/${castId}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900/50 to-black">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-black/80 backdrop-blur-sm border-b border-gold/20">
        <div className="max-w-7xl mx-auto px-4 md:px-8 py-4 flex items-center justify-between">
          <Button
            onClick={() => {
              sessionStorage.setItem('brillar-internal-nav', 'true');
              navigate('/');
            }}
            variant="ghost"
            className="text-gold hover:text-gold-light hover:bg-gold/10 transition-all duration-300"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            {language === 'en' ? 'Back to Home' : 'ホームに戻る'}
          </Button>
          
          <h1 className="text-2xl md:text-3xl font-serif text-gold gold-glow">
            {language === 'en' ? 'All Cast Members' : '全キャストメンバー'}
          </h1>
          
          <div className="w-24"></div> {/* Spacer for centering */}
        </div>
      </div>

      {/* Main Content */}
      <div 
        ref={ref}
        className={`max-w-7xl mx-auto px-4 md:px-8 py-12 reveal ${isInView || isMobile ? 'active' : ''}`}
      >
        {/* Hero Section */}
        <div className="text-center mb-16" style={{ animationDelay: isMobile ? '0s' : '0.2s' }}>
          <h2 className="text-3xl md:text-5xl font-serif text-gold mb-6 gold-glow">
            {language === 'en' ? 'Meet Our Beautiful Cast' : '美しいキャストをご紹介'}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-8"></div>
          <p className="text-lg md:text-xl text-gold-light max-w-3xl mx-auto leading-relaxed">
            {language === 'en' 
              ? 'Discover the elegance and charm of our exceptional hostesses, each bringing their unique personality and grace to create unforgettable experiences.'
              : '格別なホステスたちの優雅さと魅力を発見してください。それぞれが独自の個性と優美さを持ち、忘れられない体験を創り出します。'
            }
          </p>
        </div>

        {/* Shop Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {['All', 'BRILLAR', 'lucia', 'vega', 'lumi'].map((shop) => (
            <Button
              key={shop}
              onClick={() => handleShopFilter(shop)}
              className={`px-6 py-2 rounded-full font-medium transition-all duration-300 ${
                selectedShop === shop
                  ? 'bg-gold text-black shadow-[0_0_20px_rgba(212,175,55,0.5)] border-2 border-gold'
                  : 'bg-black/50 text-gold border-2 border-gold/30 hover:border-gold/60 hover:bg-gold/10'
              }`}
            >
              {shop.charAt(0).toUpperCase() + shop.slice(1)}
            </Button>
          ))}
        </div>



        {/* Cast Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
          {currentMembers.map((member, index) => (
            <div 
              key={member.id}
              className="group cursor-pointer"
              style={{ animationDelay: isMobile ? '0s' : `${0.4 + index * 0.1}s` }}
              onClick={() => handleCastClick(member.id)}
            >
              <div className="relative bg-gradient-to-br from-black/80 to-gray-900/80 rounded-xl overflow-hidden border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:shadow-[0_20px_50px_rgba(212,175,55,0.3)] hover:-translate-y-2 backdrop-blur-sm">
                {/* Image Container */}
                <div className="relative aspect-[3/4] overflow-hidden">
                  <img 
                    src={member.image} 
                    alt={member.name}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent" />
                  
                  {/* Overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-end justify-center pb-16">
                    <div className="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                      <Button 
                        className="bg-gold/20 hover:bg-gold hover:text-black border border-gold/50 hover:border-gold text-gold font-medium px-4 py-2 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-105"
                      >
                        {language === 'en' ? 'View Profile' : 'プロフィール'}
                      </Button>
                    </div>
                  </div>
                </div>
                
                {/* Info Section */}
                <div className="p-4 text-center">
                  <h3 className="text-gold text-lg font-medium mb-1 group-hover:text-gold-light transition-colors duration-300 gold-glow">
                    {language === 'en' ? member.name : member.nameJa}
                  </h3>
                  <p className="text-gold/70 text-sm mb-2">
                    {language === 'en' ? member.nickname : member.nicknameJa}
                  </p>
                
                  <div className="w-8 h-0.5 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mt-2 group-hover:w-12 transition-all duration-500"></div>
                </div>
                
                {/* Decorative corner elements */}
                <div className="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-gold/30 group-hover:border-gold/60 transition-colors duration-300"></div>
                <div className="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-gold/30 group-hover:border-gold/60 transition-colors duration-300"></div>
                <div className="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-gold/30 group-hover:border-gold/60 transition-colors duration-300"></div>
                <div className="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-gold/30 group-hover:border-gold/60 transition-colors duration-300"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination Info */}
        <div className="text-center mb-8">
          <p className="text-gold-light text-lg">
            {language === 'en'
              ? `Showing ${startIndex + 1}-${Math.min(endIndex, filteredMembers.length)} of ${filteredMembers.length} cast members`
              : `${filteredMembers.length}人中 ${startIndex + 1}-${Math.min(endIndex, filteredMembers.length)}人を表示`
            }
          </p>
          <p className="text-gold/70 text-sm mt-2">
            {language === 'en'
              ? `Page ${currentPage} of ${totalPages}`
              : `${totalPages}ページ中 ${currentPage}ページ`
            }
          </p>
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mt-16">
            {/* Previous Button */}
            <Button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              variant="ghost"
              className="text-gold hover:text-gold-light hover:bg-gold/10 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
            >
              <ChevronLeft className="w-5 h-5 mr-2" />
              <span className="hidden sm:inline">{language === 'en' ? 'Previous' : '前へ'}</span>
              <span className="sm:hidden">{language === 'en' ? 'Prev' : '前'}</span>
            </Button>

            {/* Page Numbers */}
            <div className="flex gap-1 sm:gap-2 flex-wrap justify-center">
              {Array.from({ length: Math.min(window.innerWidth < 640 ? 3 : 5, totalPages) }, (_, i) => {
                let pageNum: number;
                const maxPages = window.innerWidth < 640 ? 3 : 5;
                if (totalPages <= maxPages) {
                  pageNum = i + 1;
                } else if (currentPage <= Math.floor(maxPages / 2) + 1) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - Math.floor(maxPages / 2)) {
                  pageNum = totalPages - maxPages + 1 + i;
                } else {
                  pageNum = currentPage - Math.floor(maxPages / 2) + i;
                }

                return (
                  <Button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    variant={currentPage === pageNum ? "default" : "ghost"}
                    className={`w-8 h-8 sm:w-10 sm:h-10 p-0 text-sm transition-all duration-300 ${
                      currentPage === pageNum
                        ? 'bg-gold text-black hover:bg-gold-light'
                        : 'text-gold hover:text-gold-light hover:bg-gold/10'
                    }`}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            {/* Next Button */}
            <Button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              variant="ghost"
              className="text-gold hover:text-gold-light hover:bg-gold/10 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto"
            >
              <span className="hidden sm:inline">{language === 'en' ? 'Next' : '次へ'}</span>
              <span className="sm:hidden">{language === 'en' ? 'Next' : '次'}</span>
              <ChevronRight className="w-5 h-5 ml-2" />
            </Button>
          </div>
        )}

        {/* Page Jump */}
        {totalPages > 5 && (
          <div className="flex flex-col sm:flex-row justify-center items-center gap-2 sm:gap-4 mt-8">
            <span className="text-gold-light text-sm text-center">
              {language === 'en' ? 'Go to page:' : 'ページへ移動:'}
            </span>
            <div className="flex items-center gap-2">
              <input
                type="number"
                min="1"
                max={totalPages}
                value={currentPage}
                onChange={(e) => {
                  const page = parseInt(e.target.value);
                  if (page >= 1 && page <= totalPages) {
                    setCurrentPage(page);
                  }
                }}
                className="w-16 px-2 py-1 bg-black/50 border border-gold/30 rounded text-gold text-center focus:border-gold focus:outline-none text-sm"
              />
              <span className="text-gold-light text-sm">
                {language === 'en' ? `of ${totalPages}` : `/ ${totalPages}`}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AllCast;