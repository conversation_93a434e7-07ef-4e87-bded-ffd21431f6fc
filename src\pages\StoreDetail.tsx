import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft, X } from "lucide-react";
import { useLanguage } from "@/hooks/use-language";
import Footer from "@/components/Footer";
import StoreHero from "@/components/StoreDetail/StoreHero";
import StoreInfo from "@/components/StoreDetail/StoreInfo";
import PricingTable from "@/components/StoreDetail/PricingTable";
import { storeData } from "@/data/storeData";

const StoreDetail: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { language } = useLanguage();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const store = storeData[parseInt(id!) as keyof typeof storeData];

  useEffect(() => {
    if (!store) {
      sessionStorage.setItem('brillar-internal-nav', 'true');
      navigate('/');
    }
  }, [store, navigate]);

  useEffect(() => {
    // Temporarily disable smooth scrolling and scroll to top when component mounts
    const htmlElement = document.documentElement;
    const originalScrollBehavior = htmlElement.style.scrollBehavior;

    // Force instant scrolling
    htmlElement.style.scrollBehavior = 'auto';

    window.scrollTo({
      top: 0,
      behavior: 'auto'  // Make sure it's 'auto', not 'smooth'
    });

    // Restore original scroll behavior after a brief delay
    const timeoutId = setTimeout(() => {
      htmlElement.style.scrollBehavior = originalScrollBehavior;
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      htmlElement.style.scrollBehavior = originalScrollBehavior;
    };
  }, []);

  const handleIntelligentReturn = () => {
    const returnSource = sessionStorage.getItem('brillar-return-source');

    // Clear the return source after reading
    sessionStorage.removeItem('brillar-return-source');
    sessionStorage.setItem('brillar-internal-nav', 'true');

    if (returnSource === 'home-stores') {
      // Set target section for home page to scroll to stores section
      sessionStorage.setItem('brillar-return-target', 'stores');
      navigate('/');
    } else if (returnSource === 'home-shop-overview') {
      // Set target section for home page to scroll to shop overview section
      sessionStorage.setItem('brillar-return-target', 'shop-overview');
      navigate('/');
    } else {
      // Fallback: just go to home page
      navigate('/');
    }
  };

  if (!store) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Custom Sticky Navbar */}
      <div className="sticky top-0 z-[200] bg-black/90 backdrop-blur-sm border-b border-gold/20">
        <div className="max-w-7xl mx-auto px-4 md:px-8 py-4 flex items-center justify-between">
          <Button
            onClick={handleIntelligentReturn}
            variant="ghost"
            className="text-gold hover:text-gold-light hover:bg-gold/10 transition-all duration-300"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            {language === 'en' ? 'Back to Home' : 'ホームに戻る'}
          </Button>

          <h1 className="text-xl md:text-2xl font-serif text-gold gold-glow">
            {language === 'en' ? store.nameEn : store.name}
          </h1>

          <div className="w-24"></div> {/* Spacer for centering */}
        </div>
      </div>

      <StoreHero store={store} />
      <StoreInfo store={store} />
      <PricingTable store={store} />
      
      {/* Simple Gallery Section */}
      <section className="py-16 px-4 md:px-8 bg-gradient-to-br from-black via-gray-900/50 to-black">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-serif text-gold mb-4">
              {language === 'en' ? 'Gallery' : 'ギャラリー'}
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto"></div>
          </div>
          
          {/* Simple Grid Gallery */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {store.images.map((image, index) => (
              <div
                key={index}
                className="relative aspect-square overflow-hidden rounded-lg cursor-pointer group"
                onClick={() => setSelectedImage(image)}
              >
                <img
                  src={image}
                  alt={`${store.name} - Image ${index + 1}`}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Simple Image Modal */}
      {selectedImage && (
        <div 
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative w-full h-full max-w-5xl max-h-full flex items-center justify-center">
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-4 right-4 z-10 bg-black/50 hover:bg-black/70 text-gold w-10 h-10 rounded-full flex items-center justify-center transition-colors duration-200"
            >
              <X className="w-6 h-6" />
            </button>
            <img
              src={selectedImage}
              alt="Gallery Full Size"
              className="max-w-full max-h-[85vh] md:max-h-[90vh] object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
};

export default StoreDetail;
