
import React, { useRef, useState } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useSmoothIntersection } from "@/hooks/use-intersection-observer";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { getCastMembersForGallery } from "@/data/castData";

const Gallery: React.FC = () => {
  const { t, language } = useLanguage();
  const navigate = useNavigate();
  const ref = useRef<HTMLDivElement>(null);
  const { isIntersecting: isInView, hasTriggered } = useSmoothIntersection(ref, { threshold: 0.05 });
  
  // State for selected shop filter
  const [selectedShop, setSelectedShop] = useState<string>('All');

  // Get cast members filtered by shop (default to All)
  const actresses = getCastMembersForGallery(selectedShop === 'All' ? 8 : 12, selectedShop);

  const handleActressClick = (actressId: number) => {
    // Store the source page and section for return navigation
    sessionStorage.setItem('brillar-return-source', 'home-gallery');
    sessionStorage.setItem('brillar-internal-nav', 'true');
    navigate(`/cast/${actressId}`);
  };

  const handleViewAllCast = () => {
    sessionStorage.setItem('brillar-internal-nav', 'true');
    navigate('/cast');
  };

  const handleShopFilter = (shop: string) => {
    setSelectedShop(shop);
  };

  return (
    <section id="gallery" className="py-24 px-4 md:px-8 relative overflow-hidden">
      {/* Royal background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black" />
      <div className="absolute top-0 left-0 w-full h-full opacity-5"
           style={{
             backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30l15-15v30l-15-15zm-15 0l-15-15v30l15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
           }} />
      
      <div 
        ref={ref}
        className={`max-w-7xl mx-auto reveal relative z-10 ${isInView ? 'active' : ''}`}
      >
        <h2 className="section-heading mb-8">CASTS</h2>
        
        {/* Shop Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {['All', 'BRILLAR', 'lucia', 'vega', 'lumi'].map((shop) => (
            <Button
              key={shop}
              onClick={() => handleShopFilter(shop)}
              className={`px-6 py-2 rounded-full font-medium transition-all duration-300 ${
                selectedShop === shop
                  ? 'bg-gold text-black shadow-[0_0_20px_rgba(212,175,55,0.5)] border-2 border-gold'
                  : 'bg-black/50 text-gold border-2 border-gold/30 hover:border-gold/60 hover:bg-gold/10'
              }`}
            >
              {shop.charAt(0).toUpperCase() + shop.slice(1)}
            </Button>
          ))}
        </div>
        
        {/* Actress Grid - 4 per row on desktop, 2 on mobile - 8 for All, 12 for specific shops */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {actresses.map((actress, index) => (
            <div 
              key={actress.id}
              className="group cursor-pointer"
              style={{ animationDelay: `${index * 0.1}s` }}
              onClick={() => handleActressClick(actress.id)}
            >
              <div className="relative bg-gradient-to-br from-black/80 to-gray-900/80 rounded-xl overflow-hidden border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:shadow-[0_20px_50px_rgba(212,175,55,0.3)] hover:-translate-y-2 backdrop-blur-sm">
                {/* Image Container with fixed aspect ratio */}
                <div className="relative aspect-[3/4] overflow-hidden">
                  <img 
                    src={actress.image} 
                    alt={actress.name}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent" />
                  <div className="absolute inset-0 group-hover:shadow-[inset_0_0_50px_rgba(212,175,55,0.2)] transition-all duration-500" />
                  
                  {/* Overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex items-end justify-center pb-20">
                    <div className="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                      <Button 
                        className="bg-gold/20 hover:bg-gold hover:text-black border border-gold/50 hover:border-gold text-gold font-medium px-6 py-2 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-105"
                      >
                        View Profile
                      </Button>
                    </div>
                  </div>
                </div>
                
                {/* Name Section */}
                <div className="p-4 text-center">
                  <h3 className="text-gold text-lg font-medium mb-1 group-hover:text-gold-light transition-colors duration-300 gold-glow">
                    {language === 'en' ? actress.name : actress.nameJa}
                  </h3>
                  <div className="w-8 h-0.5 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto group-hover:w-12 transition-all duration-500"></div>
                </div>
                
                {/* Decorative corner elements */}
                <div className="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-gold/30 group-hover:border-gold/60 transition-colors duration-300"></div>
                <div className="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-gold/30 group-hover:border-gold/60 transition-colors duration-300"></div>
                <div className="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-gold/30 group-hover:border-gold/60 transition-colors duration-300"></div>
                <div className="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-gold/30 group-hover:border-gold/60 transition-colors duration-300"></div>
              </div>
            </div>
          ))}
        </div>
        
        {/* View All Cast Button */}
        <div className="text-center">
          <Button 
            onClick={handleViewAllCast}
            className="royal-button text-black font-semibold px-8 py-3 rounded-full hover:scale-105 transition-all duration-300 shadow-[0_10px_30px_rgba(212,175,55,0.3)] hover:shadow-[0_15px_40px_rgba(212,175,55,0.4)]"
          >
            {language === 'en' ? 'View All Cast' : '全キャストを見る'}
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Gallery;