
import React, { useState, useRef, useEffect } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useSmoothIntersection } from "@/hooks/use-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, MapPin, Star } from "lucide-react";
import featureImg from "../images/feature-event.webp";
import newFeature from  "../images/new-event2.jpg";
// Featured event data
const featuredEvent = {
  id: 999,
  name: "さら生誕祭",
  nameEn: "Sara Birthday Festival",
  date: "令和七年八月八日(金)",
  dateEn: "Friday, August 8, 2025",
  endDate: "2025-09-08T02:00:00",
  endDateEn: "2025-09-08T02:00:00",
  image: newFeature,
  description: <>さらの誕生日を祝う <br /> 特別な一夜 <br /> 華やかな花々に囲まれて <br /> 素敵な時間をお過ごしください</>,
  descriptionEn: "A special night celebrating <PERSON>'s birthday surrounded by beautiful flowers. Come enjoy a wonderful time with us.",
  location: "BRILLAR ",
  locationJa: "BRILLAR ",
  price: "Special Price",
  isLive: true
};

// Sample event data
const events = [
  {
    id: 1,
    title: "シャンパンナイト 🍾",
    titleEn: "Champagne Night 🍾",
    date: "2025年6月10日",
    dateEn: "June 10, 2025",
    description: "プレミアムシャンパンでスペシャルな夜をお楽しみください",
    descriptionEn: "Enjoy a special night with premium champagne",
    category: "パーティー",
    categoryEn: "Party",
    isNew: true,
  },
  {
    id: 2,
    title: "スペシャルキャンペーン ✨",
    titleEn: "Special Campaign ✨",
    date: "2025年6月15日",
    dateEn: "June 15, 2025",
    description: "限定特別価格でお楽しみいただけます",
    descriptionEn: "Special limited-time pricing available",
    category: "キャンペーン",
    categoryEn: "Campaign",
    isNew: true,
  },
  {
    id: 3,
    title: "VIPルームパーティー 👑",
    titleEn: "VIP Room Party 👑",
    date: "2025年6月20日",
    dateEn: "June 20, 2025",
    description: "プライベートVIPルームでの特別パーティー",
    descriptionEn: "Exclusive party in our private VIP room",
    category: "パーティー",
    categoryEn: "Party",
    isNew: false,
  },
  {
    id: 4,
    title: "新人ホステスお披露目会 💖",
    titleEn: "New Hostess Debut 💖",
    date: "2025年6月25日",
    dateEn: "June 25, 2025",
    description: "新しいホステスの皆様をご紹介いたします",
    descriptionEn: "Meet our newest hostesses",
    category: "イベント",
    categoryEn: "Event",
    isNew: false,
  },
];

const NoticeBoard: React.FC = () => {
  const { language } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const { isIntersecting: isInView, hasTriggered } = useSmoothIntersection(ref, { threshold: 0.05 });
  const [activeTab, setActiveTab] = useState("すべて");
  const [visibleEvents, setVisibleEvents] = useState(3);
  const [timeLeft, setTimeLeft] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  // Countdown timer effect
  useEffect(() => {
    // Use the ISO format date for proper parsing
    const targetDate = new Date(featuredEvent.endDate).getTime();
    
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const difference = targetDate - now;
      
      if (difference > 0) {
        setTimeLeft({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000)
        });
      } else {
        // Event has ended
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const filteredEvents = activeTab === "すべて" 
    ? events 
    : events.filter(event => language === 'en' ? event.categoryEn === activeTab : event.category === activeTab);

  const handleLoadMore = () => {
    setVisibleEvents(prev => Math.min(prev + 3, filteredEvents.length));
  };

  return (
    <section id="notice" className="py-24 px-4 md:px-8 relative overflow-hidden">
      {/* Royal background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black" />
      <div className="absolute top-0 left-0 w-full h-full opacity-5"
           style={{
             backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30l15-15v30l-15-15zm-15 0l-15-15v30l15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
           }} />
      
      <div 
        ref={ref}
        className={`max-w-6xl mx-auto reveal relative z-10 ${isInView ? 'active' : ''}`}
      >
        <div className="text-center mb-16">
          <h2 className="section-heading">
            {language === 'en' ? 'Notices & Events' : 'お知らせ・イベント予定'}
          </h2>
        </div>

        {/* Featured Event Hero Section */}
        <div className="mb-20">
          <div className="relative h-[600px] sm:h-[700px] md:h-[800px] lg:h-[900px] rounded-2xl overflow-hidden border border-gold/30 hover:border-gold/50 transition-all duration-500">
            {/* Background Image */}
            <div 
              className="absolute inset-0 bg-cover bg-center transition-transform duration-700 hover:scale-105"
              style={{ backgroundImage: `url(${featuredEvent.image})` }}
            />
            
            {/* Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-black/30" />
            
            {/* Live Badge */}
            <div className="absolute top-4 left-4 md:top-6 md:left-6 z-10">
              <div className="flex items-center gap-2 bg-red-600/90 backdrop-blur-sm px-3 py-1.5 md:px-4 md:py-2 rounded-full">
                <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-white rounded-full animate-pulse"></div>
                <span className="text-white font-semibold text-xs md:text-sm">
                  {language === 'en' ? 'FEATURED EVENT' : '注目イベント'}
                </span>
              </div>
            </div>

            {/* Content */}
            <div className="absolute inset-0 flex flex-col justify-end p-4 md:p-8 lg:p-12 text-white">
              <div className="max-w-2xl">
                <h3 className="text-2xl sm:text-3xl md:text-4xl lg:text-6xl font-serif text-gold mb-3 md:mb-4 gold-glow leading-tight">
                  {language === 'en' ? featuredEvent.nameEn : featuredEvent.name}
                </h3>
                
                <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gold-light mb-4 md:mb-6 leading-relaxed">
                  {language === 'en' ? featuredEvent.descriptionEn : featuredEvent.description}
                </p>

                {/* Event Details */}
                <div className="flex flex-wrap gap-3 md:gap-6 mb-6 md:mb-8 text-gold/90">
                  <div className="flex items-center gap-1.5 md:gap-2">
                    <Calendar className="w-4 h-4 md:w-5 md:h-5" />
                    <span className="text-sm md:text-base">{language === 'en' ? featuredEvent.dateEn : featuredEvent.date}</span>
                  </div>
                  <div className="flex items-center gap-1.5 md:gap-2">
                    <MapPin className="w-4 h-4 md:w-5 md:h-5" />
                    <span className="text-sm md:text-base">{language === 'en' ? featuredEvent.location : featuredEvent.locationJa}</span>
                  </div>
                  <div className="flex items-center gap-1.5 md:gap-2">
                    <Star className="w-4 h-4 md:w-5 md:h-5" />
                    <span className="text-sm md:text-base">{featuredEvent.price}</span>
                  </div>
                </div>

                {/* Countdown Timer */}
                <div className="mb-6 md:mb-8">
                  <p className="text-gold/80 mb-3 md:mb-4 text-base md:text-lg">
                    {language === 'en' ? 'Event ends in:' : 'イベント終了まで:'}
                  </p>
                  <div className="grid grid-cols-4 gap-2 md:gap-4 max-w-xs md:max-w-md">
                    {Object.entries(timeLeft).map(([unit, value]) => (
                      <div key={unit} className="bg-black/50 backdrop-blur-sm rounded-lg p-2 md:p-3 text-center border border-gold/20">
                        <div className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-gold">{value}</div>
                        <div className="text-xs text-gold/70 uppercase tracking-wide">
                          {language === 'en' 
                            ? unit === 'days' ? 'Days' : unit === 'hours' ? 'Hours' : unit === 'minutes' ? 'Min' : 'Sec'
                            : unit === 'days' ? '日' : unit === 'hours' ? '時間' : unit === 'minutes' ? '分' : '秒'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* CTA Button */}
                <Button className="royal-button text-black font-semibold px-6 md:px-8 py-3 md:py-4 text-base md:text-lg rounded-full hover:scale-105 transition-all duration-300 shadow-[0_15px_40px_rgba(212,175,55,0.4)]">
                  {language === 'en' ? 'Book Now' : '全部まとめてゲット！¥1,000！'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Notices & Updates */}
        <div className="mb-16">
          <h3 className="text-3xl md:text-4xl font-serif text-gold text-center mb-12 gold-glow">
            {language === 'en' ? 'Latest Notices & Updates' : '最新のお知らせ・更新情報'}
          </h3>

          {/* Filter Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-12">
          <TabsList className="grid w-full grid-cols-4 bg-black/60 backdrop-blur-sm border border-gold/20">
            <TabsTrigger 
              value="すべて" 
              className="data-[state=active]:bg-gold data-[state=active]:text-black text-gold font-medium"
            >
              {language === 'en' ? 'All' : 'すべて'}
            </TabsTrigger>
            <TabsTrigger 
              value={language === 'en' ? 'Party' : 'パーティー'}
              className="data-[state=active]:bg-gold data-[state=active]:text-black text-gold font-medium"
            >
              {language === 'en' ? 'Party' : 'パーティー'}
            </TabsTrigger>
            <TabsTrigger 
              value={language === 'en' ? 'Campaign' : 'キャンペーン'}
              className="data-[state=active]:bg-gold data-[state=active]:text-black text-gold font-medium"
            >
              {language === 'en' ? 'Campaign' : 'キャンペーン'}
            </TabsTrigger>
            <TabsTrigger 
              value={language === 'en' ? 'Event' : 'イベント'}
              className="data-[state=active]:bg-gold data-[state=active]:text-black text-gold font-medium"
            >
              {language === 'en' ? 'Event' : 'イベント'}
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-8">
            <div className="space-y-6">
              {filteredEvents.slice(0, visibleEvents).map((event, index) => (
                <Card 
                  key={event.id}
                  className="group cursor-pointer bg-gradient-to-r from-black/80 to-gray-900/80 border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:shadow-[0_15px_40px_rgba(212,175,55,0.25)] hover:-translate-y-1 backdrop-blur-sm"
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <h3 className="text-xl md:text-2xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300 gold-glow">
                            {language === 'en' ? event.titleEn : event.title}
                          </h3>
                          {event.isNew && (
                            <span className="bg-red-600 text-white text-xs px-2 py-1 rounded-full font-bold animate-pulse">
                              NEW
                            </span>
                          )}
                        </div>
                        <p className="text-gold/90 font-medium mb-2">
                          {language === 'en' ? event.dateEn : event.date}
                        </p>
                        <p className="text-gold-light leading-relaxed">
                          {language === 'en' ? event.descriptionEn : event.description}
                        </p>
                      </div>
                      
                      <div className="flex flex-col items-end gap-2">
                        <span className="bg-gold/20 text-gold px-3 py-1 rounded-full text-sm font-medium border border-gold/30">
                          {language === 'en' ? event.categoryEn : event.category}
                        </span>
                        <Button 
                          className="bg-gold/20 hover:bg-gold hover:text-black border border-gold/50 hover:border-gold text-gold font-medium px-6 py-2 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-105"
                        >
                          {language === 'en' ? 'Learn More' : '詳細を見る'}
                        </Button>
                      </div>
                    </div>
                    
                    {/* Decorative border */}
                    <div className="mt-4 w-0 group-hover:w-full h-0.5 bg-gradient-to-r from-gold to-transparent transition-all duration-700"></div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Load More Button */}
            {visibleEvents < filteredEvents.length && (
              <div className="text-center mt-12">
                <Button 
                  onClick={handleLoadMore}
                  className="royal-button text-black font-semibold px-8 py-3 rounded-full hover:scale-105 transition-all duration-300 shadow-[0_10px_30px_rgba(212,175,55,0.3)] hover:shadow-[0_15px_40px_rgba(212,175,55,0.4)]"
                >
                  {language === 'en' ? 'Load More' : 'もっと見る'}
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
        </div>
      </div>
    </section>
  );
};

export default NoticeBoard;
