
import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import CircularDropdownMenu from "./CircularDropdownMenu";
import logo from "../images/logo.webp"
const Navbar: React.FC = () => {
  const [scrolled, setScrolled] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  useEffect(() => {
    // Check initial scroll position with a small delay to ensure page is loaded
    const checkInitialScroll = () => {
      const scrollY = window.scrollY;
      const shouldBeScrolled = scrollY > 80;
      
      if (shouldBeScrolled) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    // Set initial state immediately
    checkInitialScroll();
    
    // Also check after a short delay to handle any loading issues
    const timeoutId = setTimeout(checkInitialScroll, 100);

    const handleScroll = () => {
      const scrollY = window.scrollY;
      if (scrollY > 80) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  const handleLogoClick = () => {
    // Check if we're already on the homepage
    if (location.pathname === '/') {
      // If on homepage, just scroll to hero section
      const heroElement = document.getElementById('hero');
      if (heroElement) {
        heroElement.scrollIntoView({ behavior: 'smooth' });
      } else {
        // Fallback: scroll to top if hero element not found
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
    } else {
      // Mark as internal navigation to prevent preloader
      sessionStorage.setItem('brillar-internal-nav', 'true');
      // If on a different route, navigate to homepage using React Router
      navigate('/', { replace: false });
    }
  };

  const handleSelectItem = (section: string) => {
    // For recruitment and notice, use custom event to trigger scroll in Index
    if (section === "recruitment" || section === "notice") {
      const event = new CustomEvent("menu-section-scroll", { detail: { section } });
      window.dispatchEvent(event);
      return;
    }
    const element = document.getElementById(section);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <nav 
      className={cn(
        "fixed top-0 left-0 w-full z-50 px-4 md:px-8 transition-all duration-300 flex items-center justify-between",
        scrolled 
          ? "bg-black/90 backdrop-blur-sm py-3 shadow-lg" 
          : "bg-transparent py-5"
      )}
    >
        {/* New Logo */}
        <div 
          className={cn(
            "transition-all duration-300 cursor-pointer", 
            scrolled ? "h-[60px] md:h-[80px]" : "h-[80px] md:h-[100px]"
          )}
          onClick={handleLogoClick}
        >
          <img 
            src={logo} 
            alt="BRILLAR" 
            className="h-full object-contain filter drop-shadow-[0_0_5px_rgba(212,175,55,0.5)]"
          />
        </div>

        {/* Circular Dropdown Menu */}
        <div className="relative z-50">
          <CircularDropdownMenu onSelectItem={handleSelectItem} />
        </div>
      </nav>
  );
};

export default Navbar;
