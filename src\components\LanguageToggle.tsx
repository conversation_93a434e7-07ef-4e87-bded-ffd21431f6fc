
import React from "react";
import { useLanguage } from "@/hooks/use-language";

const LanguageToggle: React.FC = () => {
  const { language, setLanguage } = useLanguage();
  
  return (
    <button
      className="text-gold hover:text-gold-light transition-colors duration-200 flex items-center gap-2"
      onClick={() => setLanguage(language === 'ja' ? 'en' : 'ja')}
    >
      {language === 'ja' ? 'EN' : '日本語'}
    </button>
  );
};

export default LanguageToggle;
