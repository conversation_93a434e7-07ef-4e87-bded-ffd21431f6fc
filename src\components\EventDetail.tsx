
import React, { useState, useRef, useEffect } from "react";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Share2, Facebook, Instagram, Twitter, X } from "lucide-react";

interface EventDetailProps {
  event: {
    id: number;
    name: string;
    nameEn: string;
    date: string;
    dateEn: string;
    image: string;
    description: string;
    descriptionEn: string;
    gallery?: string[];
  };
  onClose: () => void;
}

// Fallback gallery images for events without specific gallery
const fallbackGallery = [
  "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
  "https://images.unsplash.com/photo-1571624436279-b272aff752b5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
];

const EventDetail: React.FC<EventDetailProps> = ({ event, onClose }) => {
  const { language } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.1 });
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  const gallery = event.gallery || fallbackGallery;

  useEffect(() => {
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';

    // Scroll to top of the modal when it opens with instant behavior
    if (modalRef.current) {
      modalRef.current.scrollTo({
        top: 0,
        behavior: 'auto'
      });
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  // Additional effect to handle lightbox modal scroll prevention
  useEffect(() => {
    if (selectedImage) {
      // Prevent scrolling when lightbox is open
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';
    } else {
      // Restore scrolling when lightbox is closed
      document.body.style.overflow = 'unset';
      document.documentElement.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
      document.documentElement.style.overflow = 'unset';
    };
  }, [selectedImage]);

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const text = language === 'en' ? event.nameEn : event.name;

    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`);
        break;
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`);
        break;
      case 'instagram':
        // Instagram doesn't support direct sharing, so we'll copy to clipboard
        navigator.clipboard.writeText(`${text} - ${url}`);
        break;
    }
    setShowShareMenu(false);
  };

  // Handle image selection and loading state
  const handleImageSelect = (image: string) => {
    setSelectedImage(image);
    setImageLoading(true);
    setImageError(false);
    
    // Prevent any scrolling and ensure modal appears at current viewport
    document.body.style.position = 'fixed';
    document.body.style.top = `-${window.scrollY}px`;
    document.body.style.width = '100%';
  };

  // Handle image load success
  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  // Handle image load error
  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  // Handle lightbox close
  const handleLightboxClose = () => {
    // Restore scroll position
    const scrollY = document.body.style.top;
    document.body.style.position = '';
    document.body.style.top = '';
    document.body.style.width = '';
    window.scrollTo(0, parseInt(scrollY || '0') * -1);
    
    setSelectedImage(null);
    setImageLoading(false);
    setImageError(false);
  };

  return (
    <div ref={modalRef} className="fixed inset-0 bg-black/95 backdrop-blur-sm z-50 overflow-y-auto">
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900/50 to-black">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-black/80 backdrop-blur-sm border-b border-gold/20">
          <div className="max-w-7xl mx-auto px-4 md:px-8 py-4 flex items-center justify-between">
            <Button
              onClick={onClose}
              variant="ghost"
              className="text-gold hover:text-gold-light hover:bg-gold/10 transition-all duration-300"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              {language === 'en' ? 'Back to Events' : 'イベントに戻る'}
            </Button>
            
            <div className="relative">
              <Button
                onClick={() => setShowShareMenu(!showShareMenu)}
                variant="ghost"
                className="text-gold hover:text-gold-light hover:bg-gold/10 transition-all duration-300"
              >
                <Share2 className="w-5 h-5 mr-2" />
                {language === 'en' ? 'Share' : 'シェア'}
              </Button>
              
              {showShareMenu && (
                <div className="absolute right-0 top-full mt-2 bg-black/90 border border-gold/30 rounded-lg p-2 flex gap-2">
                  <Button
                    onClick={() => handleShare('facebook')}
                    size="sm"
                    variant="ghost"
                    className="text-gold hover:text-gold-light"
                  >
                    <Facebook className="w-4 h-4" />
                  </Button>
                  <Button
                    onClick={() => handleShare('twitter')}
                    size="sm"
                    variant="ghost"
                    className="text-gold hover:text-gold-light"
                  >
                    <Twitter className="w-4 h-4" />
                  </Button>
                  <Button
                    onClick={() => handleShare('instagram')}
                    size="sm"
                    variant="ghost"
                    className="text-gold hover:text-gold-light"
                  >
                    <Instagram className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div 
          ref={ref}
          className={`max-w-7xl mx-auto px-4 md:px-8 py-12 reveal ${isInView ? 'active' : ''}`}
        >
          {/* Hero Section */}
          <div className="text-center mb-16" style={{ animationDelay: '0.2s' }}>
            <h1 className="text-4xl md:text-6xl font-serif text-gold mb-6 gold-glow">
              {language === 'en' ? event.nameEn : event.name}
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-8"></div>
            <p className="text-xl md:text-2xl text-gold/90 mb-4">
              {language === 'en' ? event.dateEn : event.date}
            </p>
            <p className="text-lg md:text-xl text-gold-light max-w-3xl mx-auto leading-relaxed">
              {language === 'en' ? event.descriptionEn : event.description}
            </p>
          </div>

          {/* Gallery Section */}
          <div className="mb-16" style={{ animationDelay: '0.4s' }}>
            <h2 className="text-3xl md:text-4xl font-serif text-gold text-center mb-12 gold-glow">
              {language === 'en' ? 'Event Gallery' : 'イベントギャラリー'}
            </h2>
            
            {/* Responsive Gallery Grid */}
            <div className={`grid gap-6 ${
              gallery.length === 1
                ? 'grid-cols-1 max-w-2xl mx-auto'
                : gallery.length === 2
                ? 'grid-cols-1 md:grid-cols-2 max-w-4xl mx-auto'
                : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
            }`}>
              {gallery.map((image, index) => (
                <div
                  key={index}
                  className="group cursor-pointer"
                  style={{ animationDelay: `${0.6 + index * 0.1}s` }}
                  onClick={() => handleImageSelect(image)}
                >
                  <div className="relative overflow-hidden rounded-xl border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:shadow-[0_20px_50px_rgba(212,175,55,0.3)] hover:-translate-y-2 backdrop-blur-sm">
                    <div className="aspect-[4/3] overflow-hidden">
                      <img
                        src={image}
                        alt={`${language === 'en' ? event.nameEn : event.name} - Photo ${index + 1}`}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                      />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="text-gold font-medium bg-black/50 px-4 py-2 rounded-full backdrop-blur-sm">
                        {language === 'en' ? 'View Full Size' : '拡大表示'}
                      </div>
                    </div>

                    {/* Photo counter for multiple images */}
                    {gallery.length > 1 && (
                      <div className="absolute top-4 right-4 bg-black/70 text-gold px-2 py-1 rounded-full text-sm font-medium">
                        {index + 1} / {gallery.length}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Back Button */}
          <div className="text-center" style={{ animationDelay: '0.8s' }}>
            <Button
              onClick={onClose}
              className="royal-button text-black font-semibold px-8 py-3 rounded-full hover:scale-105 transition-all duration-300 shadow-[0_10px_30px_rgba(212,175,55,0.3)] hover:shadow-[0_15px_40px_rgba(212,175,55,0.4)]"
            >
              {language === 'en' ? 'Back to Events' : 'イベント一覧に戻る'}
            </Button>
          </div>
        </div>
      </div>

      {/* Simple Image Modal Popup */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/90 z-[100] flex items-center justify-center p-4"
          style={{ 
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0
          }}
          onClick={handleLightboxClose}
        >
          {/* Close Button */}
          <button
            onClick={handleLightboxClose}
            className="absolute top-4 right-4 z-10 bg-gold hover:bg-gold-light text-black w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg"
            aria-label={language === 'en' ? 'Close image' : '画像を閉じる'}
          >
            <X className="w-5 h-5" />
          </button>

          {/* Image Container */}
          <div className="relative max-w-[90vw] max-h-[80vh] flex items-center justify-center">
            {/* Loading State */}
            {imageLoading && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-8 border-2 border-gold/30 border-t-gold rounded-full animate-spin"></div>
              </div>
            )}

            {/* Error State */}
            {imageError && (
              <div className="text-red-400 text-center p-8">
                <p>{language === 'en' ? 'Failed to load image' : '画像の読み込みに失敗しました'}</p>
              </div>
            )}

            {/* Main Image */}
            <img
              src={selectedImage}
              alt={`${language === 'en' ? event.nameEn : event.name} - Full size`}
              className={`max-w-full max-h-full object-contain rounded-lg shadow-2xl transition-opacity duration-300 ${
                imageLoading ? 'opacity-0' : 'opacity-100'
              }`}
              onLoad={handleImageLoad}
              onError={handleImageError}
              onClick={(e) => e.stopPropagation()}
            />
          </div>

          {/* Simple Instructions */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
            <p className="text-gold/70 text-sm bg-black/50 px-4 py-2 rounded-full backdrop-blur-sm">
              {language === 'en' ? 'Tap anywhere to close' : 'タップして閉じる'}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EventDetail;
