
import React, { useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import emailjs from '@emailjs/browser';
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Mail, Phone, User, MessageSquare, Send, Check, X } from "lucide-react";

const ContactUs: React.FC = () => {
  const { language } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.1 });

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: ""
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [showModal, setShowModal] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.9, y: 50 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const formVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
        delay: 0.2
      }
    }
  };

  const contactInfoVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.4
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Initialize EmailJS with your public key
      emailjs.init('fteh_nXXZcDmlk37Z');

      // Send email using your template
      const result = await emailjs.send(
        'default_service', // You can change this to your actual service ID if different
        'template_jyp1f7r', // Your template ID
        {
          from_name: formData.name,
          from_email: formData.email,
          phone: formData.phone,
          subject: formData.subject,
          message: formData.message,
          to_email: '<EMAIL>', // Your email
        }
      );

      console.log('Email sent successfully:', result);
      setSubmitStatus('success');
      setShowModal(true);
      
      // Reset form
      setFormData({
        name: "",
        email: "",
        phone: "",
        subject: "",
        message: ""
      });

      // Auto-hide modal after 4 seconds
      setTimeout(() => {
        setShowModal(false);
        setSubmitStatus('idle');
      }, 4000);

    } catch (error) {
      console.error('Email sending failed:', error);
      setSubmitStatus('error');
      setShowModal(true);
      
      // Auto-hide modal after 4 seconds
      setTimeout(() => {
        setShowModal(false);
        setSubmitStatus('idle');
      }, 4000);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Success/Error Modal Component
  const StatusModal = () => (
    <AnimatePresence>
      {showModal && (
        <motion.div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          onClick={() => setShowModal(false)}
        >
          <motion.div
            className="bg-gradient-to-br from-gray-900 to-black border-2 border-gold/30 rounded-2xl p-8 max-w-md mx-4 text-center relative overflow-hidden"
            initial={{ scale: 0.5, opacity: 0, y: 50 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.5, opacity: 0, y: 50 }}
            transition={{ 
              type: "spring", 
              damping: 25, 
              stiffness: 300,
              duration: 0.6 
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Background decoration */}
            <div className="absolute inset-0 bg-gradient-to-br from-gold/5 to-transparent"></div>
            <div className="absolute -top-10 -right-10 w-32 h-32 bg-gold/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-10 -left-10 w-32 h-32 bg-gold/10 rounded-full blur-xl"></div>
            
            <div className="relative z-10">
              {/* Animated Icon */}
              <motion.div
                className={`w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center ${
                  submitStatus === 'success' 
                    ? 'bg-gradient-to-br from-green-500 to-green-600' 
                    : 'bg-gradient-to-br from-red-500 to-red-600'
                }`}
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ 
                  delay: 0.2, 
                  type: "spring", 
                  damping: 15, 
                  stiffness: 300 
                }}
              >
                {submitStatus === 'success' ? (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.5, duration: 0.3 }}
                  >
                    <Check className="w-10 h-10 text-white" strokeWidth={3} />
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.5, duration: 0.3 }}
                  >
                    <X className="w-10 h-10 text-white" strokeWidth={3} />
                  </motion.div>
                )}
              </motion.div>

              {/* Status Text */}
              <motion.h3
                className={`text-2xl font-bold mb-4 ${
                  submitStatus === 'success' ? 'text-green-400' : 'text-red-400'
                }`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
              >
                {submitStatus === 'success' 
                  ? (language === 'en' ? 'Success!' : '成功！') 
                  : (language === 'en' ? 'Failed!' : '失敗！')
                }
              </motion.h3>

              <motion.p
                className="text-gray-300 text-lg leading-relaxed mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.5 }}
              >
                {submitStatus === 'success' 
                  ? (language === 'en' 
                      ? 'Your message has been sent to us, we will contact you soon.' 
                      : 'メッセージが送信されました。すぐにご連絡いたします。'
                    )
                  : (language === 'en' 
                      ? 'Your message failed to send, please try again.' 
                      : 'メッセージの送信に失敗しました。再度お試しください。'
                    )
                }
              </motion.p>

              {/* Close Button */}
              <motion.button
                onClick={() => setShowModal(false)}
                className="px-6 py-3 bg-gradient-to-r from-gold to-gold-light text-black font-semibold rounded-lg hover:from-gold-light hover:to-gold transition-all duration-300 shadow-lg"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.8, duration: 0.3 }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {language === 'en' ? 'Close' : '閉じる'}
              </motion.button>

              {/* Auto-close indicator */}
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-gold to-gold-light"
                initial={{ width: "100%" }}
                animate={{ width: "0%" }}
                transition={{ duration: 4, ease: "linear" }}
              ></motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return (
    <>
      <StatusModal />
      <motion.section 
      ref={ref}
      className={`relative py-20 px-4 md:px-8 bg-black overflow-hidden reveal ${isInView ? 'active' : ''}`}
      id="contact"
      variants={containerVariants}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
    >
      {/* Elegant Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(212,175,55,0.15)_0%,transparent_50%)]"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(212,175,55,0.1)_0%,transparent_50%)]"></div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <motion.div 
          className="text-center mb-16"
          variants={itemVariants}
        >
          <div className="inline-block mb-6">
            <motion.div 
              className="w-24 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-4"
              initial={{ scaleX: 0 }}
              animate={isInView ? { scaleX: 1 } : { scaleX: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            ></motion.div>
            <motion.h2 
              className="text-4xl md:text-6xl font-edwardian font-bold text-gold gold-glow mb-4"
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {language === 'en' ? 'Contact Us' : 'お問い合わせ'}
            </motion.h2>
            <motion.div 
              className="w-32 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto"
              initial={{ scaleX: 0 }}
              animate={isInView ? { scaleX: 1 } : { scaleX: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            ></motion.div>
          </div>
          <motion.p 
            className="text-xl md:text-2xl text-gray-300 font-light max-w-3xl mx-auto leading-relaxed"
            variants={itemVariants}
          >
            {language === 'en' 
              ? 'Experience the extraordinary. Reach out to us for reservations or inquiries.' 
              : '非日常の体験をお求めの方は、ご予約・お問い合わせをお待ちしております。'
            }
          </motion.p>
        </motion.div>

        {/* Contact Form */}
        <motion.div 
          className="max-w-4xl mx-auto"
          variants={formVariants}
        >
          <motion.div variants={cardVariants}>
            <Card className="bg-black/60 backdrop-blur-sm border-2 border-gold/30 shadow-2xl overflow-hidden relative">
              {/* Ornate border decoration */}
              <div className="absolute inset-0 border-4 border-gold/20 rounded-lg pointer-events-none">
                <div className="absolute -top-2 -left-2 w-6 h-6 border-l-4 border-t-4 border-gold/40 rounded-tl-lg"></div>
                <div className="absolute -top-2 -right-2 w-6 h-6 border-r-4 border-t-4 border-gold/40 rounded-tr-lg"></div>
                <div className="absolute -bottom-2 -left-2 w-6 h-6 border-l-4 border-b-4 border-gold/40 rounded-bl-lg"></div>
                <div className="absolute -bottom-2 -right-2 w-6 h-6 border-r-4 border-b-4 border-gold/40 rounded-br-lg"></div>
              </div>

              <CardContent className="p-8 md:p-12">
                <form onSubmit={handleSubmit} className="space-y-8">
                  {/* Name and Email Row */}
                  <motion.div 
                    className="grid grid-cols-1 md:grid-cols-2 gap-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ duration: 0.6, delay: 0.6 }}
                  >
                    <motion.div 
                      className="space-y-3"
                      whileFocus={{ scale: 1.02 }}
                      transition={{ duration: 0.2 }}
                    >
                      <label className="flex items-center text-gold font-medium text-lg">
                        <User className="w-5 h-5 mr-2" />
                        {language === 'en' ? 'Name' : 'お名前'}
                        <span className="text-red-400 ml-1">*</span>
                      </label>
                      <Input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="h-12 bg-black/50 border-gold/30 focus:border-gold text-white placeholder:text-gray-400 text-lg transition-all duration-300"
                        placeholder={language === 'en' ? 'Your full name' : 'お名前をご入力ください'}
                      />
                    </motion.div>

                    <motion.div 
                      className="space-y-3"
                      whileFocus={{ scale: 1.02 }}
                      transition={{ duration: 0.2 }}
                    >
                      <label className="flex items-center text-gold font-medium text-lg">
                        <Mail className="w-5 h-5 mr-2" />
                        {language === 'en' ? 'Email' : 'メールアドレス'}
                        <span className="text-red-400 ml-1">*</span>
                      </label>
                      <Input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="h-12 bg-black/50 border-gold/30 focus:border-gold text-white placeholder:text-gray-400 text-lg transition-all duration-300"
                        placeholder={language === 'en' ? '<EMAIL>' : '<EMAIL>'}
                      />
                    </motion.div>
                  </motion.div>

                  {/* Phone and Subject Row */}
                  <motion.div 
                    className="grid grid-cols-1 md:grid-cols-2 gap-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ duration: 0.6, delay: 0.7 }}
                  >
                    <motion.div 
                      className="space-y-3"
                      whileFocus={{ scale: 1.02 }}
                      transition={{ duration: 0.2 }}
                    >
                      <label className="flex items-center text-gold font-medium text-lg">
                        <Phone className="w-5 h-5 mr-2" />
                        {language === 'en' ? 'Phone Number' : '電話番号'}
                      </label>
                      <Input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="h-12 bg-black/50 border-gold/30 focus:border-gold text-white placeholder:text-gray-400 text-lg transition-all duration-300"
                        placeholder={language === 'en' ? '+81 90-1234-5678' : '090-1234-5678'}
                      />
                    </motion.div>

                    <motion.div 
                      className="space-y-3"
                      whileFocus={{ scale: 1.02 }}
                      transition={{ duration: 0.2 }}
                    >
                      <label className="flex items-center text-gold font-medium text-lg">
                        <MessageSquare className="w-5 h-5 mr-2" />
                        {language === 'en' ? 'Subject' : '件名'}
                        <span className="text-red-400 ml-1">*</span>
                      </label>
                      <Input
                        type="text"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                        className="h-12 bg-black/50 border-gold/30 focus:border-gold text-white placeholder:text-gray-400 text-lg transition-all duration-300"
                        placeholder={language === 'en' ? 'Reservation, Inquiry, etc.' : 'ご予約、お問い合わせなど'}
                      />
                    </motion.div>
                  </motion.div>

                  {/* Message */}
                  <motion.div 
                    className="space-y-3"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ duration: 0.6, delay: 0.8 }}
                  >
                    <label className="flex items-center text-gold font-medium text-lg">
                      <MessageSquare className="w-5 h-5 mr-2" />
                      {language === 'en' ? 'Message' : 'メッセージ'}
                      <span className="text-red-400 ml-1">*</span>
                    </label>
                    <motion.div whileFocus={{ scale: 1.01 }} transition={{ duration: 0.2 }}>
                      <Textarea
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                        rows={6}
                        className="bg-black/50 border-gold/30 focus:border-gold text-white placeholder:text-gray-400 text-lg resize-none transition-all duration-300"
                        placeholder={language === 'en' 
                          ? 'Please share your inquiry, reservation details, or any questions you may have...' 
                          : 'ご質問、ご予約の詳細、その他ご不明な点がございましたらお聞かせください...'
                        }
                      />
                    </motion.div>
                  </motion.div>

                  {/* Submit Button */}
                  <motion.div 
                    className="text-center pt-6"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ duration: 0.6, delay: 0.9 }}
                  >
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="px-12 py-4 bg-gradient-to-r from-gold via-gold-light to-gold text-black font-bold text-lg hover:from-gold-light hover:to-gold transition-all duration-300 shadow-lg hover:shadow-gold/25 group disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Send className={`w-5 h-5 mr-3 group-hover:translate-x-1 transition-transform duration-300 ${isSubmitting ? 'animate-pulse' : ''}`} />
                        {isSubmitting 
                          ? (language === 'en' ? 'Sending...' : '送信中...') 
                          : (language === 'en' ? 'Send Message' : 'メッセージを送信')
                        }
                      </Button>
                    </motion.div>
                  </motion.div>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* Contact Information */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 max-w-6xl mx-auto"
          variants={contactInfoVariants}
        >
          <motion.div 
            className="text-center space-y-4 p-6 bg-black/40 backdrop-blur-sm rounded-lg border border-gold/20"
            variants={itemVariants}
            whileHover={{ 
              scale: 1.05,
              boxShadow: "0 10px 30px rgba(212, 175, 55, 0.2)",
              borderColor: "rgba(212, 175, 55, 0.4)"
            }}
            transition={{ duration: 0.3 }}
          >
            <motion.div 
              className="w-16 h-16 bg-gradient-to-br from-gold to-gold-light rounded-full flex items-center justify-center mx-auto"
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.6 }}
            >
              <Phone className="w-8 h-8 text-black" />
            </motion.div>
            <h3 className="text-xl font-serif text-gold">{language === 'en' ? 'Phone' : '電話'}</h3>
            <p className="text-gray-300 text-lg">************</p>
          </motion.div>

          <motion.div 
            className="text-center space-y-4 p-6 bg-black/40 backdrop-blur-sm rounded-lg border border-gold/20"
            variants={itemVariants}
            whileHover={{ 
              scale: 1.05,
              boxShadow: "0 10px 30px rgba(212, 175, 55, 0.2)",
              borderColor: "rgba(212, 175, 55, 0.4)"
            }}
            transition={{ duration: 0.3 }}
          >
            <motion.div 
              className="w-16 h-16 bg-gradient-to-br from-gold to-gold-light rounded-full flex items-center justify-center mx-auto"
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.6 }}
            >
              <Mail className="w-8 h-8 text-black" />
            </motion.div>
            <h3 className="text-xl font-serif text-gold">{language === 'en' ? 'Email' : 'メール'}</h3>
            <p className="text-gray-300 text-lg"><EMAIL></p>
          </motion.div>

          <motion.div 
            className="text-center space-y-4 p-6 bg-black/40 backdrop-blur-sm rounded-lg border border-gold/20"
            variants={itemVariants}
            whileHover={{ 
              scale: 1.05,
              boxShadow: "0 10px 30px rgba(212, 175, 55, 0.2)",
              borderColor: "rgba(212, 175, 55, 0.4)"
            }}
            transition={{ duration: 0.3 }}
          >
            <motion.div 
              className="w-16 h-16 bg-gradient-to-br from-gold to-gold-light rounded-full flex items-center justify-center mx-auto"
              whileHover={{ rotate: 360 }}
              transition={{ duration: 0.6 }}
            >
              <MessageSquare className="w-8 h-8 text-black" />
            </motion.div>
            <h3 className="text-xl font-serif text-gold">{language === 'en' ? 'Hours' : '営業時間'}</h3>
            <p className="text-gray-300 text-lg">21:00 - 03:30 (日曜日は定休日です)</p>
          </motion.div>
        </motion.div>
      </div>

      {/* Decorative corner elements */}
      <motion.div 
        className="absolute top-8 left-8 w-16 h-16 border-l-2 border-t-2 border-gold/30 opacity-60"
        initial={{ opacity: 0, scale: 0 }}
        animate={isInView ? { opacity: 0.6, scale: 1 } : { opacity: 0, scale: 0 }}
        transition={{ duration: 0.8, delay: 1.0 }}
      ></motion.div>
      <motion.div 
        className="absolute top-8 right-8 w-16 h-16 border-r-2 border-t-2 border-gold/30 opacity-60"
        initial={{ opacity: 0, scale: 0 }}
        animate={isInView ? { opacity: 0.6, scale: 1 } : { opacity: 0, scale: 0 }}
        transition={{ duration: 0.8, delay: 1.1 }}
      ></motion.div>
      <motion.div 
        className="absolute bottom-8 left-8 w-16 h-16 border-l-2 border-b-2 border-gold/30 opacity-60"
        initial={{ opacity: 0, scale: 0 }}
        animate={isInView ? { opacity: 0.6, scale: 1 } : { opacity: 0, scale: 0 }}
        transition={{ duration: 0.8, delay: 1.2 }}
      ></motion.div>
      <motion.div 
        className="absolute bottom-8 right-8 w-16 h-16 border-r-2 border-b-2 border-gold/30 opacity-60"
        initial={{ opacity: 0, scale: 0 }}
        animate={isInView ? { opacity: 0.6, scale: 1 } : { opacity: 0, scale: 0 }}
        transition={{ duration: 0.8, delay: 1.3 }}
      ></motion.div>
    </motion.section>
    </>
  );
};

export default ContactUs;
