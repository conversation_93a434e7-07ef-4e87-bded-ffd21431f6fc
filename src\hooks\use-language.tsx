
import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';

// Define translations
const translations = {
  en: {
    about: 'About',
    cast: 'Cast',
    news: 'News',
    access: 'Access',
    heroTagline: 'Welcome to the Extraordinary',
    discover: 'Discover',
    aboutTitle: 'Welcome to the Premier Hostess Club',
    aboutText1: 'BRILLAR offers an unparalleled experience in the heart of Tokyo\'s nightlife district.',
    aboutText2: 'Our carefully selected hostesses provide elegant and sophisticated company in a luxurious atmosphere.',
    aboutText3: 'Immerse yourself in a world of refined pleasure and exceptional service.',
    viewAllCast: 'View All Cast',
    address: 'Address',
    addressValue: 'BRILLAR Building B1F, 1-2-3 Azabu, Minato-ku, Tokyo',
    phone: 'Phone',
    phoneValue: '03-1234-5678',
    hours: 'Hours',
    hoursValue: '19:00 - Last',
    closed: 'Closed: Sundays',
  },
  ja: {
    about: '当店について',
    cast: 'キャスト',
    news: 'ニュース',
    access: 'アクセス',
    heroTagline: '非日常へようこそ',
    discover: 'ご案内',
    aboutTitle: '最高級のホステスクラブへようこそ',
    aboutText1: '当店は東京随一の美女を集め、上質で華やかなひとときを提供いたします。',
    aboutText2: '洗練された空間で、厳選されたホステスたちが優雅なおもてなしを致します。',
    aboutText3: '至福のひとときをお過ごしください。',
    viewAllCast: 'キャスト一覧',
    address: '住所',
    addressValue: '東京都港区麻布1-2-3 BRILLERビル B1F',
    phone: '電話番号',
    phoneValue: '03-1234-5678',
    hours: '営業時間',
    hoursValue: '19:00 - ラスト',
    closed: '定休日: 日曜日',
  },
};

// Create language context
type LanguageContextType = {
  language: 'en' | 'ja';
  setLanguage: (language: 'en' | 'ja') => void;
  t: (key: string) => string;
};

const LanguageContext = createContext<LanguageContextType>({
  language: 'ja',
  setLanguage: () => {},
  t: () => '',
});

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<'en' | 'ja'>('ja');

  // Function to get translation
  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations.en] || key;
  };

  // Update document language attribute
  useEffect(() => {
    document.documentElement.lang = language;
  }, [language]);

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => useContext(LanguageContext);
